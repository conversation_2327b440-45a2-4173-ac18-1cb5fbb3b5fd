<template>
	<!-- 列表项 -->
	<view class="post-container" :data-post-id="res.postId" @tap="onGoToDetail">
		<view class="post-author-date">
			<image class="post-author" :src="res.avatar" mode="aspectFill"/>
			<text class="post-date">{{ res.date }}</text>
		</view>
		<text class="post-title">{{ res.title }}</text>
		<image class="post-image" :src="res.imgSrc" mode="aspectFill"/>
		<text class="post-content">{{ res.content }}</text>
		<view class="post-like">
			<uni-icons type="heart" size="16" color="#666"></uni-icons>
			<text class="post-like-font">{{ res.reading }}</text>
			<uni-icons type="eye" size="16" color="#666"></uni-icons>
			<text class="post-like-font">{{ res.collection }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'Post',
		/**
		 * 组件的属性列表
		 */
		props: {
			// text属性 - 简写，只设置类型，默认值是类型默认值
			text: {
				type: String,
				default: ''
			},
			// res对象属性
			res: {
				type: Object,
				required: true,
				default: () => ({
					postId: '',
					avatar: '',
					date: '',
					title: '',
					imgSrc: '',
					content: '',
					reading: 0,
					collection: 0
				})
			}
		},
		/**
		 * 组件的初始数据
		 */
		data() {
			return {

			}
		},
		/**
		 * 组件的方法列表
		 */
		methods: {
			/**
			 * 点击list列表项后，获取ID并跳转到详情页
			 */
			onGoToDetail(event) {
				// event:事件对象
				console.log(event)
				console.log(event.currentTarget.dataset.postId)
				const pid = event.currentTarget.dataset.postId
				// 跳转 - 使用uni-app的导航方法
				uni.navigateTo({
					url: '/pages/posts-detail/posts-detail?pid=' + pid,
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* components/post/index.wxss */
	.post-container{
	  display: flex;
	  flex-direction: column;
	  margin-top: 20rpx;
	  margin-bottom: 20rpx;
	  background-color:snow;
	  /* border-top: 1rpx solid #333; */
	  border-bottom: 1rpx solid #333;
	  padding-bottom: 10rpx;
	}
	.post-author-date{
	  margin: 10rpx 0 10rpx 20rpx;
	  display: flex;
	  flex-direction: row;
	  align-items: center;
	}
	.post-author{
	  width: 60rpx;
	  height: 60rpx;
	}
	.post-date{
	  font-size: 26rpx;
	  margin-left: 24rpx;
	}
	.post-title{
	  /* border: 1rpx solid red; */
	  font-size: 40rpx;
	  font-weight: 700;
	  margin-bottom: 20rpx;
	  margin-left: 20rpx;
	}
	.post-image{
	  width: 100%;
	  height: 400rpx;
	  margin-bottom:20rpx;
	}
	.post-content{
	  font-size: 30rpx;
	  color: #666;
	  margin-bottom: 20rpx;
	  line-height: 40rpx;
	  margin-left: 20rpx;
	  margin-right: 20rpx;
	  letter-spacing: 2rpx;
	}
	.post-like{
	  display: flex;
	  flex-direction: row;
	  font-size: 22rpx;
	  line-height: 16rpx;
	  margin-left: 20rpx;
	  height: 30rpx;
	}
	.post-like-font{
	  margin-left: 10rpx;
	  margin-right: 20rpx;
	}
</style>