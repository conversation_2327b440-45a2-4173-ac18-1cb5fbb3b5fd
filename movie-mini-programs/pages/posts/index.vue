<template>
	<view>
		<swiper autoplay indicator-dots interval="4000">
			<swiper-item>
				<image
					src="https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/7867f916735f0dc542b970fe5bbed169.jpg?f=webp&w=1080&h=540&bg=B0C10"
					mode="" />
			</swiper-item>
			<swiper-item>
				<image
					src="https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/0ca94a1dadb79bd258970c207ba4b7fb.jpg?f=webp&w=1080&h=540&bg=D2C8C7"
					mode="" />
			</swiper-item>
			<swiper-item>
				<image
					src="https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/ed48aeb066c5d7a5be1209063b943db1.jpg?f=webp&w=1080&h=540&bg=706051"
					mode="" />
			</swiper-item>
		</swiper>
		<view v-for="(item, index) in postList" :key="index">
			<Post :res="item" />
		</view>
	</view>
</template>

<script>
	import Post from '../../components/post/index.vue'
	import { postList } from '../../static/data/posts-data.js'
	export default {
		components: {
			Post
		},
		data() {
			return {
				postList: postList
			}
		},
		onLoad() {},
	}
</script>

<style scoped lang="scss">
	swiper {
		width: 100%;
		height: 400rpx;
	}

	swiper image {
		width: 100%;
		height: 100%;
	}
</style>