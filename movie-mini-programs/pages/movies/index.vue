<template>
	<view class="movies-container">
		<!-- 正在热映 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">正在热映</text>
				<text class="more-btn" @click="goToMoviesList('in_theaters')">更多></text>
			</view>
			<view class="movies-row" v-if="!loadingInTheaters">
				<view
					v-for="(movie, index) in inTheatersMovies.slice(0, 3)"
					:key="movie.id"
					class="movie-item"
					@click="goToDetail(movie)"
				>
					<view class="movie-poster">
						<image
							:src="movie.images.large"
							mode="aspectFill"
							class="poster-image"
						/>
					</view>
					<view class="movie-title">{{ movie.title }}</view>
					<view class="movie-rating">
						<view class="stars">
							<text
								v-for="star in 5"
								:key="star"
								class="star"
								:class="{ 'filled': star <= Math.floor(movie.rating.average / 2) }"
							>★</text>
						</view>
						<text class="rating-number">{{ movie.rating.average }}</text>
					</view>
				</view>
			</view>
			<view class="loading-row" v-if="loadingInTheaters">
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 即将上映 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">即将上映</text>
				<text class="more-btn" @click="goToMoviesList('coming_soon')">更多></text>
			</view>
			<view class="movies-row" v-if="!loadingComingSoon">
				<view
					v-for="(movie, index) in comingSoonMovies.slice(0, 3)"
					:key="movie.id"
					class="movie-item"
					@click="goToDetail(movie)"
				>
					<view class="movie-poster">
						<image
							:src="movie.images.large"
							mode="aspectFill"
							class="poster-image"
						/>
					</view>
					<view class="movie-title">{{ movie.title }}</view>
					<view class="movie-rating">
						<view class="stars">
							<text
								v-for="star in 5"
								:key="star"
								class="star"
								:class="{ 'filled': star <= Math.floor(movie.rating.average / 2) }"
							>★</text>
						</view>
						<text class="rating-number">{{ movie.rating.average }}</text>
					</view>
				</view>
			</view>
			<view class="loading-row" v-if="loadingComingSoon">
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- TOP100 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">TOP100</text>
				<text class="more-btn" @click="goToMoviesList('top250')">更多></text>
			</view>
			<view class="movies-row" v-if="!loadingTop250">
				<view
					v-for="(movie, index) in top250Movies.slice(0, 3)"
					:key="movie.id"
					class="movie-item"
					@click="goToDetail(movie)"
				>
					<view class="movie-poster">
						<image
							:src="movie.images.large"
							mode="aspectFill"
							class="poster-image"
						/>
					</view>
					<view class="movie-title">{{ movie.title }}</view>
					<view class="movie-rating">
						<view class="stars">
							<text
								v-for="star in 5"
								:key="star"
								class="star"
								:class="{ 'filled': star <= Math.floor(movie.rating.average / 2) }"
							>★</text>
						</view>
						<text class="rating-number">{{ movie.rating.average }}</text>
					</view>
				</view>
			</view>
			<view class="loading-row" v-if="loadingTop250">
				<text class="loading-text">加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				inTheatersMovies: [],
				comingSoonMovies: [],
				top250Movies: [],
				loadingInTheaters: false,
				loadingComingSoon: false,
				loadingTop250: false
			}
		},

		onLoad() {
			this.loadAllMovies()
		},

		methods: {
			// 加载所有分类的电影数据
			async loadAllMovies() {
				await Promise.all([
					this.loadInTheaters(),
					this.loadComingSoon(),
					this.loadTop250()
				])
			},

			// 加载正在热映
			async loadInTheaters() {
				this.loadingInTheaters = true
				try {
					const response = await uni.request({
						url: 'http://t.talelin.com/v2/movie/in_theaters',
						method: 'GET'
					})

					if (response.statusCode === 200 && response.data.subjects) {
						this.inTheatersMovies = response.data.subjects
					}
				} catch (error) {
					console.error('正在热映请求失败:', error)
				} finally {
					this.loadingInTheaters = false
				}
			},

			// 加载即将上映
			async loadComingSoon() {
				this.loadingComingSoon = true
				try {
					const response = await uni.request({
						url: 'http://t.talelin.com/v2/movie/coming_soon',
						method: 'GET'
					})

					if (response.statusCode === 200 && response.data.subjects) {
						this.comingSoonMovies = response.data.subjects
					}
				} catch (error) {
					console.error('即将上映请求失败:', error)
				} finally {
					this.loadingComingSoon = false
				}
			},

			// 加载TOP250
			async loadTop250() {
				this.loadingTop250 = true
				try {
					const response = await uni.request({
						url: 'http://t.talelin.com/v2/movie/top250',
						method: 'GET'
					})

					if (response.statusCode === 200 && response.data.subjects) {
						this.top250Movies = response.data.subjects
					}
				} catch (error) {
					console.error('TOP250请求失败:', error)
				} finally {
					this.loadingTop250 = false
				}
			},

			// 跳转到详情页
			goToDetail(movie) {
				uni.navigateTo({
					url: `/pages/movie-details/index?id=${movie.id}`
				})
			},

			// 跳转到电影列表页面
			goToMoviesList(category) {
				uni.navigateTo({
					url: `/pages/movies-list/index?category=${category}`
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.movies-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}

	.section {
		margin-bottom: 60rpx;

		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;

			.section-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
			}

			.more-btn {
				font-size: 28rpx;
				color: #666;
			}
		}

		.movies-row {
			display: flex;
			justify-content: space-between;
			gap: 20rpx;
		}

		.loading-row {
			display: flex;
			justify-content: center;
			padding: 40rpx 0;

			.loading-text {
				color: #999;
				font-size: 28rpx;
			}
		}
	}

	.movie-item {
		flex: 1;
		max-width: 220rpx;

		.movie-poster {
			width: 100%;
			height: 300rpx;
			border-radius: 12rpx;
			overflow: hidden;
			margin-bottom: 16rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

			.poster-image {
				width: 100%;
				height: 100%;
			}
		}

		.movie-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
			margin-bottom: 12rpx;
			line-height: 1.3;
			text-align: center;
			// 限制显示两行，超出显示省略号
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			line-clamp: 2;
			overflow: hidden;
		}

		.movie-rating {
			display: flex;
			align-items: center;
      justify-content: space-around;

			.stars {
				display: flex;
				margin-bottom: 8rpx;

				.star {
					font-size: 24rpx;
					color: #ddd;
					margin-right: 2rpx;

					&.filled {
						color: #ffc107;
					}
				}
			}

			.rating-number {
				font-size: 26rpx;
				color: #333;
				font-weight: 500;
			}
		}
	}
</style>