<template>
	<view class="welcome-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="star star1"></view>
			<view class="star star2"></view>
			<view class="star star3"></view>
			<view class="star star4"></view>
			<view class="star star5"></view>
		</view>

		<!-- 主要内容区域 -->
		<view class="content">
			<!-- Logo区域 -->
			<view class="logo-section">
				<view class="logo-wrapper">
					<text class="logo-icon">🎬</text>
				</view>
				<view class="title-section">
					<text class="main-title">微影院</text>
					<text class="subtitle">发现精彩电影世界</text>
				</view>
			</view>

			<!-- 特色介绍 -->
			<view class="features">
				<view class="feature-item">
					<text class="feature-icon">🎭</text>
					<text class="feature-text">海量影片</text>
				</view>
				<view class="feature-item">
					<text class="feature-icon">⭐</text>
					<text class="feature-text">精选推荐</text>
				</view>
				<view class="feature-item">
					<text class="feature-icon">🎪</text>
					<text class="feature-text">观影体验</text>
				</view>
			</view>
		</view>

		<!-- 底部按钮区域 -->
		<view class="bottom-section">
			<button class="start-btn" @click="startJourney">
				<text class="btn-text">开启小程序之旅</text>
				<text class="btn-icon">→</text>
			</button>
			<text class="welcome-text">欢迎来到微影院，开始您的观影之旅</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		onLoad() {
			// 页面加载动画
			this.playWelcomeAnimation();
		},
		methods: {
			startJourney() {
				uni.reLaunch({
					url: '/pages/posts/index'
				})
			},

			playWelcomeAnimation() {
				// 添加页面进入动画效果
				const animation = uni.createAnimation({
					duration: 1000,
					timingFunction: 'ease-out'
				});

				animation.opacity(1).translateY(0).step();
				this.animationData = animation.export();
			}
		}
	}
</script>

<style scoped lang="scss">
	page {
		height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.welcome-container {
		position: relative;
		height: 100vh;
		display: flex;
		flex-direction: column;
		overflow: hidden;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	/* 背景装饰星星 */
	.bg-decoration {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none;
	}

	.star {
		position: absolute;
		width: 4rpx;
		height: 4rpx;
		background: rgba(255, 255, 255, 0.8);
		border-radius: 50%;
		animation: twinkle 2s infinite;
	}

	.star1 { top: 20%; left: 10%; animation-delay: 0s; }
	.star2 { top: 30%; right: 15%; animation-delay: 0.5s; }
	.star3 { top: 60%; left: 20%; animation-delay: 1s; }
	.star4 { top: 80%; right: 25%; animation-delay: 1.5s; }
	.star5 { top: 40%; left: 70%; animation-delay: 0.8s; }

	@keyframes twinkle {
		0%, 100% { opacity: 0.3; transform: scale(1); }
		50% { opacity: 1; transform: scale(1.2); }
	}

	/* 主要内容区域 */
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 0 60rpx;
		animation: fadeInUp 1.2s ease-out;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(60rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* Logo区域 */
	.logo-section {
		text-align: center;
		margin-bottom: 80rpx;
	}

	.logo-wrapper {
		margin-bottom: 40rpx;
		animation: bounce 2s infinite;
	}

	@keyframes bounce {
		0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
		40% { transform: translateY(-20rpx); }
		60% { transform: translateY(-10rpx); }
	}

	.logo-icon {
		font-size: 120rpx;
		line-height: 1;
	}

	.title-section {
		text-align: center;
	}

	.main-title {
		display: block;
		font-size: 64rpx;
		font-weight: bold;
		color: #ffffff;
		margin-bottom: 20rpx;
		text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
		letter-spacing: 4rpx;
	}

	.subtitle {
		display: block;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.9);
		font-weight: 300;
		letter-spacing: 2rpx;
	}

	/* 特色介绍 */
	.features {
		display: flex;
		justify-content: space-around;
		width: 100%;
		max-width: 500rpx;
	}

	.feature-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		animation: fadeIn 1.5s ease-out;
	}

	.feature-item:nth-child(1) { animation-delay: 0.3s; }
	.feature-item:nth-child(2) { animation-delay: 0.6s; }
	.feature-item:nth-child(3) { animation-delay: 0.9s; }

	@keyframes fadeIn {
		from { opacity: 0; }
		to { opacity: 1; }
	}

	.feature-icon {
		font-size: 48rpx;
		margin-bottom: 16rpx;
	}

	.feature-text {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
		font-weight: 300;
	}

	/* 底部按钮区域 */
	.bottom-section {
		padding: 60rpx;
		text-align: center;
		animation: slideUp 1.5s ease-out;
	}

	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(100rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.start-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(45deg, #ff6b6b, #ee5a24);
		border: none;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(238, 90, 36, 0.4);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
	}

	.start-btn::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s;
	}

	.start-btn:active::before {
		left: 100%;
	}

	.start-btn:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 12rpx rgba(238, 90, 36, 0.6);
	}

	.btn-text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: 500;
		margin-right: 16rpx;
	}

	.btn-icon {
		font-size: 28rpx;
		color: #ffffff;
		font-weight: bold;
		transition: transform 0.3s ease;
	}

	.start-btn:active .btn-icon {
		transform: translateX(8rpx);
	}

	.welcome-text {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		line-height: 1.5;
	}
</style>
