<template>
	<view class="movie-detail-container">
		<!-- 加载状态 -->
		<view class="loading-section" v-if="loading">
			<view class="loading-poster"></view>
			<view class="loading-info">
				<view class="loading-title"></view>
				<view class="loading-meta"></view>
				<view class="loading-desc"></view>
			</view>
		</view>

		<!-- 电影详情内容 -->
		<view class="movie-detail-content" v-if="!loading && movieDetail">
			<!-- 电影海报和基本信息 -->
			<view class="movie-header">
				<view class="movie-poster">
					<image
						:src="(movieDetail.images && movieDetail.images.large) || movieDetail.image"
						mode="aspectFill"
						class="poster-image"
						:lazy-load="true"
					/>
					<view class="rating-badge">
						{{ (movieDetail.rating && movieDetail.rating.average) || '暂无评分' }}
					</view>
				</view>

				<view class="movie-basic-info">
					<text class="movie-title">{{ movieDetail.title || movieDetail.original_title }}</text>
					<text class="movie-original-title" v-if="movieDetail.original_title && movieDetail.original_title !== movieDetail.title">
						{{ movieDetail.original_title }}
					</text>

					<view class="movie-meta">
						<text class="movie-year">{{ getYear(movieDetail.year) }}</text>
						<text class="movie-genres">{{ getGenres(movieDetail.genres) }}</text>
						<text class="movie-duration" v-if="movieDetail.durations && movieDetail.durations.length > 0">
							{{ movieDetail.durations[0] }}
						</text>
					</view>

					<view class="movie-rating-detail" v-if="movieDetail.rating">
						<view class="stars">
							<text
								v-for="star in 5"
								:key="star"
								class="star"
								:class="{ 'filled': star <= Math.floor((movieDetail.rating && movieDetail.rating.average) / 2) }"
							>★</text>
						</view>
						<text class="rating-number">{{ movieDetail.rating && movieDetail.rating.average }}</text>
						<text class="rating-count">({{ movieDetail.reviews_count }}人评价)</text>
					</view>
				</view>
			</view>

			<!-- 操作按钮区域 -->
			<view class="action-buttons">
				<view class="action-btn" @click="toggleCollect">
					<uni-icons
						:type="isCollected ? 'star-filled' : 'star'"
						:color="isCollected ? '#ffc107' : '#666'"
						size="24"
					></uni-icons>
					<text class="action-text" :class="{ 'active': isCollected }">
						{{ isCollected ? '已收藏' : '收藏' }}
					</text>
				</view>

				<view class="action-btn" @click="shareMovie">
					<uni-icons type="redo" color="#666" size="24"></uni-icons>
					<text class="action-text">分享</text>
				</view>
			</view>

			<!-- 导演和演员信息 -->
			<view class="movie-credits">
				<view class="credits-section" v-if="movieDetail.directors && movieDetail.directors.length > 0">
					<text class="credits-label">导演</text>
					<view class="credits-list">
						<view
							v-for="(director, index) in movieDetail.directors"
							:key="director.id"
							class="credit-item"
						>
							<image
								:src="(director.avatars.large) || '/static/images/default-avatar.png'"
								mode="aspectFill"
								class="credit-avatar"
							/>
							<text class="credit-name">{{ director.name }}</text>
						</view>
					</view>
				</view>

				<view class="credits-section" v-if="movieDetail.casts && movieDetail.casts.length > 0">
					<text class="credits-label">主演</text>
					<view class="credits-list">
						<view
							v-for="(cast, index) in movieDetail.casts.slice(0, 6)"
							:key="cast.id"
							class="credit-item"
						>
							<image
								:src="(cast.avatars && cast.avatars.large) || '/static/images/default-avatar.png'"
								mode="aspectFill"
								class="credit-avatar"
							/>
							<text class="credit-name">{{ cast.name }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 剧情简介 -->
			<view class="movie-summary" v-if="movieDetail.summary">
				<text class="summary-title">剧情简介</text>
				<text class="summary-content" :class="{ 'expanded': summaryExpanded }">{{ movieDetail.summary }}</text>
				<text class="summary-toggle" @click="toggleSummary" v-if="movieDetail.summary.length > 100">
					{{ summaryExpanded ? '收起' : '展开' }}
				</text>
			</view>

			<!-- 电影标签 -->
			<view class="movie-tags" v-if="movieDetail.tags && movieDetail.tags.length > 0">
				<text class="tags-title">电影标签</text>
				<view class="tags-list">
					<text
						v-for="(tag, index) in movieDetail.tags.slice(0, 8)"
						:key="index"
						class="tag-item"
					>
						{{ tag.name }}
					</text>
				</view>
			</view>

			<!-- 上映信息 -->
			<view class="movie-release-info" v-if="movieDetail.countries && movieDetail.countries.length > 0">
				<text class="release-title">制片国家/地区</text>
				<view class="release-list">
					<text
						v-for="(country, index) in movieDetail.countries"
						:key="index"
						class="release-item"
					>
						{{ country }}
					</text>
				</view>
			</view>
		</view>

		<!-- 错误状态 -->
		<view class="error-section" v-if="!loading && !movieDetail">
			<uni-icons type="info-filled" size="80" color="#ccc"></uni-icons>
			<text class="error-text">{{ errorMessage || '电影信息加载失败' }}</text>
			<button class="retry-btn" @click="loadMovieDetail">重新加载</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				movieId: '',
				movieDetail: null,
				loading: false,
				errorMessage: '',
				summaryExpanded: false,
				// 点赞和收藏相关数据
				isLiked: false,
				likeCount: 0,
				isCollected: false
			}
		},

		onLoad(options) {
			// 获取传递的电影ID
			this.movieId = options.id
			if (this.movieId) {
				this.loadMovieDetail()
				this.loadUserActions()
			} else {
				this.errorMessage = '缺少电影ID参数'
			}
		},

		methods: {
			// 加载电影详情数据
			async loadMovieDetail() {
				if (!this.movieId) return

				this.loading = true
				this.errorMessage = ''

				try {
					const response = await uni.request({
						url: `http://t.talelin.com/v2/movie/subject/${this.movieId}`,
						method: 'GET'
					})

					if (response.statusCode === 200 && response.data) {
						this.movieDetail = response.data
						// 设置页面标题
						uni.setNavigationBarTitle({
							title: this.movieDetail.title || '电影详情'
						})
					} else {
						this.errorMessage = '电影信息加载失败'
					}
				} catch (error) {
					console.error('加载电影详情失败:', error)
					this.errorMessage = '网络错误，请检查网络连接'
				} finally {
					this.loading = false
				}
			},

			// 获取年份
			getYear(year) {
				return year || '未知'
			},

			// 获取类型
			getGenres(genres) {
				return genres && genres.length > 0 ? genres.join(' / ') : '未分类'
			},

			// 切换剧情简介展开状态
			toggleSummary() {
				this.summaryExpanded = !this.summaryExpanded
			},

			// 加载用户操作状态（点赞、收藏）
			loadUserActions() {
				if (!this.movieId) return

				// 从本地存储获取点赞状态
				const likedMovies = uni.getStorageSync('likedMovies') || {}
				this.isLiked = !!likedMovies[this.movieId]

				// 从本地存储获取收藏状态
				const collectedMovies = uni.getStorageSync('collectedMovies') || {}
				this.isCollected = !!collectedMovies[this.movieId]

				// 模拟点赞数量（实际项目中应该从服务器获取）
				this.likeCount = Math.floor(Math.random() * 1000) + 100
			},

			// 切换点赞状态
			toggleLike() {
				if (!this.movieId) return

				this.isLiked = !this.isLiked

				// 更新点赞数量
				if (this.isLiked) {
					this.likeCount += 1
				} else {
					this.likeCount -= 1
				}

				// 保存到本地存储
				const likedMovies = uni.getStorageSync('likedMovies') || {}
				if (this.isLiked) {
					likedMovies[this.movieId] = {
						movieId: this.movieId,
						title: this.movieDetail?.title,
						timestamp: Date.now()
					}
				} else {
					delete likedMovies[this.movieId]
				}
				uni.setStorageSync('likedMovies', likedMovies)

				// 显示提示
				uni.showToast({
					title: this.isLiked ? '点赞成功' : '取消点赞',
					icon: 'none',
					duration: 1500
				})
			},

			// 切换收藏状态
			toggleCollect() {
				if (!this.movieId || !this.movieDetail) return

				this.isCollected = !this.isCollected

				// 保存到本地存储
				const collectedMovies = uni.getStorageSync('collectedMovies') || {}
				if (this.isCollected) {
					collectedMovies[this.movieId] = {
						id: this.movieId,
						title: this.movieDetail.title,
						image: this.movieDetail.images?.large || this.movieDetail.image,
						rating: this.movieDetail.rating?.average,
						year: this.movieDetail.year,
						genres: this.movieDetail.genres,
						timestamp: Date.now()
					}
				} else {
					delete collectedMovies[this.movieId]
				}
				uni.setStorageSync('collectedMovies', collectedMovies)

				// 显示提示
				uni.showToast({
					title: this.isCollected ? '收藏成功' : '取消收藏',
					icon: 'success',
					duration: 1500
				})
			},

			// 分享电影
			shareMovie() {
				if (!this.movieDetail) return

				// 在小程序中，直接提示用户使用右上角分享功能
				uni.showModal({
					title: '分享电影',
					content: '请点击右上角的"..."按钮，选择"转发"来分享这部电影给好友',
					showCancel: false,
					confirmText: '知道了'
				})
			}
		},

		// 小程序分享配置
		onShareAppMessage() {
			if (!this.movieDetail) {
				return {
					title: '精彩电影推荐',
					path: `/pages/movie-details/index?id=${this.movieId}`
				}
			}

			return {
				title: `推荐电影：${this.movieDetail.title}`,
				path: `/pages/movie-details/index?id=${this.movieId}`,
				imageUrl: this.movieDetail.images?.large || this.movieDetail.image
			}
		},

		// 分享到朋友圈（如果支持）
		onShareTimeline() {
			if (!this.movieDetail) {
				return {
					title: '精彩电影推荐'
				}
			}

			return {
				title: `推荐电影：${this.movieDetail.title}`,
				imageUrl: this.movieDetail.images?.large || this.movieDetail.image
			}
		}
	}
</script>

<style scoped lang="scss">
	.movie-detail-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
		padding: 20rpx;
	}

	/* 加载状态样式 */
	.loading-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 60rpx 40rpx;
		background: #fff;
		border-radius: 20rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

		.loading-poster {
			width: 300rpx;
			height: 420rpx;
			background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
			background-size: 200% 100%;
			animation: loading 1.5s infinite;
			border-radius: 16rpx;
			margin-bottom: 30rpx;
		}

		.loading-info {
			width: 100%;

			.loading-title, .loading-meta, .loading-desc {
				height: 40rpx;
				background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
				background-size: 200% 100%;
				animation: loading 1.5s infinite;
				border-radius: 8rpx;
				margin-bottom: 20rpx;
			}

			.loading-title {
				width: 80%;
			}

			.loading-meta {
				width: 60%;
			}

			.loading-desc {
				width: 100%;
				height: 120rpx;
			}
		}
	}

	@keyframes loading {
		0% {
			background-position: -200% 0;
		}
		100% {
			background-position: 200% 0;
		}
	}

	/* 电影详情内容样式 */
	.movie-detail-content {
		.movie-header {
			display: flex;
			background: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

			.movie-poster {
				position: relative;
				width: 240rpx;
				height: 340rpx;
				border-radius: 16rpx;
				overflow: hidden;
				margin-right: 30rpx;
				flex-shrink: 0;

				.poster-image {
					width: 100%;
					height: 100%;
				}

				.rating-badge {
					position: absolute;
					top: 10rpx;
					right: 10rpx;
					background: rgba(255, 193, 7, 0.95);
					border-radius: 20rpx;
					font-size: 24rpx;
					font-weight: bold;
					width: 60rpx;
					height: 40rpx;
					line-height: 40rpx;
					text-align: center;
					color: #fff;
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
				}
			}

			.movie-basic-info {
				flex: 1;
				display: flex;
				flex-direction: column;

				.movie-title {
					font-size: 36rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 10rpx;
					line-height: 1.3;
				}

				.movie-original-title {
					font-size: 28rpx;
					color: #666;
					margin-bottom: 20rpx;
					font-style: italic;
				}

				.movie-meta {
					margin-bottom: 20rpx;

					text {
						display: inline-block;
						font-size: 26rpx;
						color: #666;
						margin-right: 20rpx;
						margin-bottom: 8rpx;
					}
				}

				.movie-rating-detail {
					display: flex;
					align-items: center;
					flex-wrap: wrap;

					.stars {
						display: flex;
						margin-right: 15rpx;

						.star {
							font-size: 28rpx;
							color: #ddd;
							margin-right: 4rpx;

							&.filled {
								color: #ffc107;
							}
						}
					}

					.rating-number {
						font-size: 32rpx;
						color: #333;
						font-weight: bold;
						margin-right: 10rpx;
					}

					.rating-count {
						font-size: 24rpx;
						color: #999;
					}
				}
			}
		}

		/* 操作按钮区域样式 */
		.action-buttons {
			display: flex;
			justify-content: space-around;
			align-items: center;
			background: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

			.action-btn {
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 20rpx;
				border-radius: 16rpx;
				transition: all 0.3s ease;
				cursor: pointer;

				&:active {
					transform: scale(0.95);
					background: #f8f9fa;
				}

				.action-text {
					font-size: 24rpx;
					color: #666;
					margin-top: 8rpx;
					transition: color 0.3s ease;

					&.active {
						color: #007aff;
						font-weight: bold;
					}
				}

				.action-count {
					font-size: 20rpx;
					color: #999;
					margin-top: 4rpx;
				}
			}
		}

		/* 演职人员样式 */
		.movie-credits {
			background: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

			.credits-section {
				margin-bottom: 40rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.credits-label {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 20rpx;
					display: block;
				}

				.credits-list {
					display: flex;
					flex-wrap: wrap;
					gap: 20rpx;

					.credit-item {
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 120rpx;

						.credit-avatar {
							width: 80rpx;
							height: 80rpx;
							border-radius: 50%;
							margin-bottom: 10rpx;
							border: 2rpx solid #f0f0f0;
						}

						.credit-name {
							font-size: 24rpx;
							color: #666;
							text-align: center;
							line-height: 1.2;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
						}
					}
				}
			}
		}

		/* 剧情简介样式 */
		.movie-summary {
			background: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

			.summary-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 20rpx;
				display: block;
			}

			.summary-content {
				font-size: 28rpx;
				color: #666;
				line-height: 1.6;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 4;
				overflow: hidden;

				&.expanded {
					-webkit-line-clamp: unset;
					overflow: visible;
				}
			}

			.summary-toggle {
				font-size: 26rpx;
				color: #007aff;
				margin-top: 15rpx;
				display: block;
				cursor: pointer;
			}
		}

		/* 电影标签样式 */
		.movie-tags {
			background: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

			.tags-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 20rpx;
				display: block;
			}

			.tags-list {
				display: flex;
				flex-wrap: wrap;
				gap: 15rpx;

				.tag-item {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: #fff;
					font-size: 24rpx;
					padding: 10rpx 20rpx;
					border-radius: 30rpx;
					box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
				}
			}
		}

		/* 上映信息样式 */
		.movie-release-info {
			background: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

			.release-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 20rpx;
				display: block;
			}

			.release-list {
				display: flex;
				flex-direction: column;
				gap: 10rpx;

				.release-item {
					font-size: 26rpx;
					color: #666;
					padding: 10rpx 15rpx;
					background: #f8f9fa;
					border-radius: 10rpx;
					border-left: 4rpx solid #007aff;
				}
			}
		}
	}

	/* 错误状态样式 */
	.error-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 80rpx 40rpx;
		background: #fff;
		border-radius: 20rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

		.error-text {
			font-size: 28rpx;
			color: #999;
			margin: 30rpx 0;
			text-align: center;
		}

		.retry-btn {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: #fff;
			border: none;
			border-radius: 30rpx;
			padding: 20rpx 40rpx;
			font-size: 28rpx;
			box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);

			&:active {
				transform: scale(0.98);
			}
		}
	}
</style>