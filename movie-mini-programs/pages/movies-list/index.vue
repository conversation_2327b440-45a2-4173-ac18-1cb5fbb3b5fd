<template>
	<view class="movies-list-container">
		<!-- 顶部搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<uni-icons type="search" size="20" color="#999"></uni-icons>
				<input
					class="search-input"
					placeholder="搜索电影..."
					v-model="searchKeyword"
					@input="onSearchInput"
				/>
				<text v-if="searchKeyword" class="clear-btn" @click="clearSearch">×</text>
			</view>
		</view>

		<!-- 电影列表 -->
		<view class="movies-list" v-if="!loading && filteredMovies.length > 0">
			<view
				v-for="(movie, index) in filteredMovies"
				:key="movie.id"
				class="movie-card"
				@click="goToDetail(movie)"
			>
				<view class="movie-poster">
					<image
						:src="movie.images.large"
						mode="aspectFill"
						class="poster-image"
						:lazy-load="true"
					/>
					<view class="rating-badge">
						{{ movie.rating.average }}
					</view>
				</view>

				<view class="movie-info">
					<text class="movie-title">{{ movie.title }}</text>
					<view class="movie-meta">
						<text class="movie-year">{{ getYear(movie.year) }}</text>
						<text class="movie-genres">{{ getGenres(movie.genres) }}</text>
					</view>
					<view class="movie-directors" v-if="movie.directors && movie.directors.length > 0">
						<text class="label">导演：</text>
						<text class="value">{{ getDirectors(movie.directors) }}</text>
					</view>
					<view class="movie-casts" v-if="movie.casts && movie.casts.length > 0">
						<text class="label">主演：</text>
						<text class="value">{{ getCasts(movie.casts) }}</text>
					</view>
					<view class="movie-rating">
						<view class="stars">
							<text
								v-for="star in 5"
								:key="star"
								class="star"
								:class="{ 'filled': star <= Math.floor(movie.rating.average / 2) }"
							>★</text>
						</view>
						<text class="rating-count">({{ movie.rating.count }}人评价)</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-section" v-if="loading || searchLoading">
			<view class="loading-item" v-for="i in 6" :key="i">
				<view class="loading-poster"></view>
				<view class="loading-info">
					<view class="loading-title"></view>
					<view class="loading-meta"></view>
					<view class="loading-desc"></view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-section" v-if="!loading && !searchLoading && filteredMovies.length === 0">
			<uni-icons type="film" size="80" color="#ccc"></uni-icons>
			<text class="empty-text">{{ isSearching ? '没有找到相关电影' : '暂无电影数据' }}</text>
			<button v-if="isSearching" class="retry-btn" @click="clearSearch">清除搜索</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				category: '', // 电影分类
				movies: [], // 所有电影数据（分类数据）
				searchResults: [], // 搜索结果数据
				searchKeyword: '', // 搜索关键词
				loading: false, // 加载状态
				searchLoading: false, // 搜索加载状态
				isSearching: false, // 是否在搜索状态
				searchTimer: null, // 搜索防抖定时器
				categoryTitles: {
					'in_theaters': '正在热映',
					'coming_soon': '即将上映',
					'top250': 'TOP100'
				}
			}
		},

		computed: {
			// 分类标题
			categoryTitle() {
				return this.categoryTitles[this.category] || '电影列表'
			},

			// 显示的电影列表（根据是否在搜索状态返回不同数据）
			filteredMovies() {
				if (this.isSearching) {
					return this.searchResults
				}
				return this.movies
			}
		},

		onLoad(options) {
			// 获取传递的分类参数
			this.category = options.category || 'in_theaters'

			// 设置页面标题
			uni.setNavigationBarTitle({
				title: this.categoryTitle
			})

			// 加载电影数据
			this.loadMovies()
		},

		// 下拉刷新
		onPullDownRefresh() {
			this.loadMovies().then(() => {
				uni.stopPullDownRefresh()
			})
		},

		// 页面卸载时清理定时器
		onUnload() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
				this.searchTimer = null
			}
		},

		methods: {
			// 加载电影数据
			async loadMovies() {
				this.loading = true

				try {
					const apiUrls = {
						'in_theaters': 'http://t.talelin.com/v2/movie/in_theaters',
						'coming_soon': 'http://t.talelin.com/v2/movie/coming_soon',
						'top250': 'http://t.talelin.com/v2/movie/top250'
					}

					const response = await uni.request({
						url: apiUrls[this.category],
						method: 'GET'
					})

					if (response.statusCode === 200 && response.data.subjects) {
						this.movies = response.data.subjects
					} else {
						this.showError('加载失败，请重试')
					}
				} catch (error) {
					console.error('加载电影数据失败:', error)
					this.showError('网络错误，请检查网络连接')
				} finally {
					this.loading = false
				}
			},

			// 搜索输入处理（添加防抖）
			onSearchInput() {
				// 清除之前的定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer)
				}

				// 设置新的定时器，500ms后执行搜索
				this.searchTimer = setTimeout(() => {
					if (this.searchKeyword.trim()) {
						this.performSearch()
					} else {
						this.clearSearch()
					}
				}, 500)
			},

			// 执行搜索
			async performSearch() {
				if (!this.searchKeyword.trim()) return

				this.searchLoading = true
				this.isSearching = true

				try {
					const response = await uni.request({
						url: `http://t.talelin.com/v2/movie/search?q=${encodeURIComponent(this.searchKeyword.trim())}`,
						method: 'GET'
					})

					if (response.statusCode === 200 && response.data.subjects) {
						this.searchResults = response.data.subjects
					} else {
						this.searchResults = []
						this.showError('搜索失败，请重试')
					}
				} catch (error) {
					console.error('搜索电影失败:', error)
					this.searchResults = []
					this.showError('网络错误，请检查网络连接')
				} finally {
					this.searchLoading = false
				}
			},

			// 清除搜索
			clearSearch() {
				this.searchKeyword = ''
				this.searchResults = []
				this.isSearching = false
				this.searchLoading = false

				// 清除定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer)
					this.searchTimer = null
				}
			},

			// 跳转到详情页
			goToDetail(movie) {
				console.log('点击电影:', movie.title)
				uni.navigateTo({
					url: `/pages/movie-details/index?id=${movie.id}`
				})
			},

			// 获取年份
			getYear(year) {
				return year || '未知'
			},

			// 获取类型
			getGenres(genres) {
				return genres && genres.length > 0 ? genres.join(' / ') : '未分类'
			},

			// 获取导演
			getDirectors(directors) {
				return directors.slice(0, 2).map(director => director.name).join(' / ')
			},

			// 获取演员
			getCasts(casts) {
				return casts.slice(0, 3).map(cast => cast.name).join(' / ')
			},

			// 显示错误信息
			showError(message) {
				uni.showToast({
					title: message,
					icon: 'none',
					duration: 2000
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.movies-list-container {
		min-height: 100vh;
	}

	// 搜索区域
	.search-section {
		padding: 30rpx 20rpx 20rpx;
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(10rpx);

		.search-box {
			display: flex;
			align-items: center;
			background: rgba(255, 255, 255, 0.9);
			border-radius: 50rpx;
			padding: 20rpx 30rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

			.search-input {
				flex: 1;
				margin-left: 20rpx;
				font-size: 28rpx;
				color: #333;

				&::placeholder {
					color: #999;
				}
			}

			.clear-btn {
				font-size: 40rpx;
				color: #999;
				margin-left: 20rpx;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	// 分类标题
	.category-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background: rgba(255, 255, 255, 0.1);

		.category-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #fff;
		}

		.movie-count {
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	}

	// 电影列表
	.movies-list {
		padding: 20rpx;
	}

	.movie-card {
		display: flex;
		background: #fff;
		border-radius: 20rpx;
		margin-bottom: 30rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
		}

		.movie-poster {
			position: relative;
			width: 200rpx;
			height: 280rpx;
			border-radius: 16rpx;
			overflow: hidden;
			margin-right: 30rpx;
			flex-shrink: 0;

			.poster-image {
				width: 100%;
				height: 100%;
			}

			.rating-badge {
				position: absolute;
				top: 10rpx;
				right: 10rpx;
				background: rgba(255, 193, 7, 0.9);
				border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: bold;
        width: 30px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        color: #fff;
			}
		}

		.movie-info {
			flex: 1;
			display: flex;
			flex-direction: column;

			.movie-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 16rpx;
				line-height: 1.4;
			}

			.movie-meta {
				display: flex;
				align-items: center;
				margin-bottom: 16rpx;

				.movie-year {
					font-size: 24rpx;
					color: #666;
					margin-right: 20rpx;
				}

				.movie-genres {
					font-size: 24rpx;
					color: #999;
				}
			}

			.movie-directors,
			.movie-casts {
				display: flex;
				margin-bottom: 12rpx;
				font-size: 26rpx;

				.label {
					color: #666;
					margin-right: 10rpx;
					flex-shrink: 0;
				}

				.value {
					color: #333;
					flex: 1;
				}
			}

			.movie-rating {
				display: flex;
				align-items: center;
				margin-top: auto;

				.stars {
					display: flex;
					margin-right: 16rpx;

					.star {
						font-size: 28rpx;
						color: #ddd;
						margin-right: 4rpx;

						&.filled {
							color: #ffc107;
						}
					}
				}

				.rating-count {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}

	// 加载状态
	.loading-section {
		padding: 20rpx;

		.loading-item {
			display: flex;
			background: #fff;
			border-radius: 20rpx;
			margin-bottom: 30rpx;
			padding: 30rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

			.loading-poster {
				width: 200rpx;
				height: 280rpx;
				background: #f0f0f0;
				border-radius: 16rpx;
				margin-right: 30rpx;
				animation: shimmer 1.5s infinite;
			}

			.loading-info {
				flex: 1;

				.loading-title {
					height: 40rpx;
					background: #f0f0f0;
					border-radius: 8rpx;
					margin-bottom: 20rpx;
					animation: shimmer 1.5s infinite;
				}

				.loading-meta {
					height: 30rpx;
					width: 60%;
					background: #f0f0f0;
					border-radius: 8rpx;
					margin-bottom: 16rpx;
					animation: shimmer 1.5s infinite;
				}

				.loading-desc {
					height: 30rpx;
					width: 80%;
					background: #f0f0f0;
					border-radius: 8rpx;
					animation: shimmer 1.5s infinite;
				}
			}
		}
	}

	// 空状态
	.empty-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 40rpx;
		text-align: center;

		.empty-text {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
			margin: 30rpx 0;
		}

		.retry-btn {
			background: rgba(255, 255, 255, 0.2);
			color: #fff;
			border: 2rpx solid rgba(255, 255, 255, 0.3);
			border-radius: 50rpx;
			padding: 20rpx 40rpx;
			font-size: 28rpx;
		}
	}

	// 动画效果
	@keyframes shimmer {
		0% {
			opacity: 1;
		}
		50% {
			opacity: 0.5;
		}
		100% {
			opacity: 1;
		}
	}
</style>