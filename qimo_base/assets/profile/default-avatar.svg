<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 阴影 -->
  <defs>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="4"/>
      <feOffset dx="0" dy="2"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆 -->
  <circle cx="200" cy="200" r="190" fill="#F5F5F5" filter="url(#shadow)"/>
  
  <!-- 头部 -->
  <circle cx="200" cy="170" r="70" fill="#E0E0E0"/>
  
  <!-- 身体 -->
  <path d="M200 260C155.818 260 120 295.818 120 340V400H280V340C280 295.818 244.182 260 200 260Z" fill="#E0E0E0"/>
</svg> 