<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 顶部头像和昵称区域 -->
  <view class="profile-header">
    <image class="avatar" src="{{avatarUrl || '/images/default_avatar.png'}}" mode="aspectFill" bindtap="onChangeAvatar"></image>
    <view class="nickname-large">{{nickname || '未设置昵称'}}</view>
    <view class="signature-small">{{signature || '这个人很懒，什么都没写~'}}</view>
    <view class="header-actions">
      <!-- 右上角操作，根据UI图是三个点和一个设置图标，这里暂时用一个示意 -->
      <image class="action-icon" src="/images/more_options_icon.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 信息列表 -->
  <view class="info-list">
    <view class="info-item" bindtap="onEditNickname">
      <text class="info-label">昵称</text>
      <view class="info-value-container">
        <text class="info-value">{{nickname || '未设置昵称'}}</text>
        <image class="arrow-right" src="/images/arrow_right.png" mode="aspectFit"></image>
      </view>
    </view>

    <view class="info-item" bindtap="onEditSignature">
      <text class="info-label">个性签名</text>
      <view class="info-value-container">
        <text class="info-value">{{signature || '这个人很懒，什么都没写~'}}</text>
        <image class="arrow-right" src="/images/arrow_right.png" mode="aspectFit"></image>
      </view>
    </view>

    <view class="info-item" bindtap="onEditGender">
      <text class="info-label">性别</text>
      <view class="info-value-container">
        <text class="info-value">{{gender || '未设置'}}</text>
        <image class="arrow-right" src="/images/arrow_right.png" mode="aspectFit"></image>
      </view>
    </view>

    <picker mode="date" value="{{birthday}}" start="1900-01-01" end="{{endDate}}" bindchange="onBirthdayChange">
      <view class="info-item">
        <text class="info-label">生日</text>
        <view class="info-value-container">
          <text class="info-value">{{birthday || '未设置'}}</text>
          <image class="arrow-right" src="/images/arrow_right.png" mode="aspectFit"></image>
        </view>
      </view>
    </picker>

    <view class="info-item" bindtap="onChooseLocation">
      <text class="info-label">地区</text>
      <view class="info-value-container">
        <text class="info-value">{{region || '未设置'}}</text>
        <image class="arrow-right" src="/images/arrow_right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="save-button" type="primary" bindtap="onSaveChanges">保存修改</button>
    <button class="reset-button" bindtap="onResetInfo">重置信息</button>
  </view>
</view>