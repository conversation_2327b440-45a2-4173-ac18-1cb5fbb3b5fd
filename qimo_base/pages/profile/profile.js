// pages/profile/profile.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    avatarUrl: '', // 初始为空，可以设置一个默认头像路径
    nickname: '',
    signature: '',
    gender: '', // '男', '女', '保密'
    birthday: '', // YYYY-MM-DD
    region: '',
    endDate: new Date().toISOString().split('T')[0], // 生日选择器的结束日期为今天
    themes: [
      { name: 'theme1', color: 'linear-gradient(to bottom right, #87CEFA, #ADD8E6)' }, // 示例浅蓝色
      { name: 'theme2', color: 'linear-gradient(to bottom right, #FFD700, #FFA500)' }, // 示例橙黄色
      { name: 'theme3', color: 'linear-gradient(to bottom right, #FFB6C1, #FF69B4)' }, // 示例粉红色
      { name: 'theme4', color: 'linear-gradient(to bottom right, #9370DB, #8A2BE2)' }, // 示例紫色
      { name: 'theme5', color: 'linear-gradient(to bottom right, #778899, #2F4F4F)' }, // 示例深灰色
      { name: 'theme6', color: 'linear-gradient(to bottom right, #00FFFF, #008B8B)' }  // 示例青色
    ],
    currentTheme: 'theme1', // 默认主题
    // 实际项目中，以上信息应该从全局状态管理或本地存储读取
    defaultAvatar: '/images/default_avatar.png' // 定义默认头像路径，确保此图片存在
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 页面加载时，可以尝试从本地存储加载用户信息
    this.loadUserInfo();
    // 设置 picker 的 endDate 为当前日期
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    this.setData({
      endDate: `${year}-${month}-${day}`
    });
  },

  loadUserInfo() {
    // 示例：尝试从storage加载数据，如果用户之前保存过
    const storedUserInfo = wx.getStorageSync('userInfo');
    if (storedUserInfo) {
      this.setData({
        avatarUrl: storedUserInfo.avatarUrl || this.data.defaultAvatar,
        nickname: storedUserInfo.nickname || '未设置昵称',
        signature: storedUserInfo.signature || '这个人很懒，什么都没写~',
        gender: storedUserInfo.gender || '未设置',
        birthday: storedUserInfo.birthday || '未设置',
        region: storedUserInfo.region || '未设置',
        currentTheme: storedUserInfo.currentTheme || 'theme1'
      });
    } else {
      // 如果没有存储信息，设置初始默认值
      this.setData({
        avatarUrl: this.data.defaultAvatar,
        nickname: '未设置昵称',
        signature: '这个人很懒，什么都没写~',
        gender: '未设置',
        birthday: '未设置',
        region: '未设置'
      });
    }
  },

  onChangeAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          avatarUrl: res.tempFiles[0].tempFilePath
        });
      },
      fail: (err) => {
        console.error('选择头像失败', err);
        if (err.errMsg !== "chooseMedia:fail cancel") {
            wx.showToast({ title: '选择失败', icon: 'none' });
        }
      }
    });
  },

  onEditNickname() {
    wx.showModal({
      title: '设置昵称',
      content: '', // 无需额外内容，因为 editable 为 true 时会显示输入框
      editable: true,
      placeholderText: '请输入您的昵称',
      success: (res) => {
        if (res.confirm && res.content) {
          this.setData({
            nickname: res.content
          });
        } else if (res.confirm && !res.content) {
            wx.showToast({ title: '昵称不能为空', icon: 'none' });
        }
      }
    });
  },

  onEditSignature() {
    wx.showModal({
      title: '设置个性签名',
      editable: true,
      placeholderText: '请输入您的个性签名',
      success: (res) => {
        if (res.confirm && res.content) {
          this.setData({
            signature: res.content
          });
        } else if (res.confirm && !res.content) {
            // 允许签名为空，或者根据需求提示
            this.setData({
                signature: '这个人很懒，什么都没写~' // 如果允许空，则设置默认值
            });
        }
      }
    });
  },

  onEditGender() {
    wx.showActionSheet({
      itemList: ['男', '女', '保密'],
      success: (res) => {
        let genderText = '保密';
        if (res.tapIndex === 0) genderText = '男';
        if (res.tapIndex === 1) genderText = '女';
        this.setData({
          gender: genderText
        });
      },
      fail: (err) => {
        console.log('选择性别操作取消或失败', err);
      }
    });
  },

  onBirthdayChange(e) {
    this.setData({
      birthday: e.detail.value
    });
  },

  onChooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          // 可以选择只显示 name 或者更详细的 address
          region: res.name || res.address
        });
      },
      fail: (err) => {
        console.error('选择地区失败', err);
        if (err.errMsg.includes('auth deny')) {
            wx.showToast({ title: '请授权位置信息', icon: 'none' });
        } else if (err.errMsg !== "chooseLocation:fail cancel") {
            wx.showToast({ title: '选择失败', icon: 'none' });
        }
      }
    });
  },

  onSelectTheme(e) {
    const selectedTheme = e.currentTarget.dataset.theme;
    this.setData({
      currentTheme: selectedTheme
    });
    // 可以在这里改变页面整体主题色，例如通过修改顶层view的class或style
    // 不过UI中主题色主要体现在顶部背景，这里仅保存选择
    // 如果顶部背景需要动态根据主题变化，则 profile-header 的背景设置需要JS动态控制或更复杂的CSS选择器
  },

  onSaveChanges() {
    // 实际项目中，这里会将 this.data 中的用户信息保存到服务器或本地存储
    const userInfoToSave = {
        avatarUrl: this.data.avatarUrl,
        nickname: this.data.nickname,
        signature: this.data.signature,
        gender: this.data.gender,
        birthday: this.data.birthday,
        region: this.data.region,
        currentTheme: this.data.currentTheme
    };
    wx.setStorageSync('userInfo', userInfoToSave);
    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500
    });
  },

  onResetInfo() {
    // 重置信息，可以恢复到初始默认值或上一次保存的状态
    // 这里简单重置为初始默认值，并清除本地存储
    wx.removeStorageSync('userInfo'); 
    this.setData({
      avatarUrl: this.data.defaultAvatar,
      nickname: '未设置昵称',
      signature: '这个人很懒，什么都没写~',
      gender: '未设置',
      birthday: '未设置',
      region: '未设置',
      currentTheme: 'theme1' // 重置为默认主题
    });
    wx.showToast({
      title: '信息已重置',
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})