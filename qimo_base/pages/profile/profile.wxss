/* pages/profile/profile.wxss */
page {
  background-color: #f4f4f4; /* UI背景色偏灰白 */
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  color: #333;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh; /* 确保内容不足一屏时，背景色也能撑满 */
}

/* 顶部头像和昵称区域 */
.profile-header {
  background: linear-gradient(180deg, #87CEFA 0%, #ADD8E6 60%, #FFFFFF 100%); /* 根据UI图调整渐变色 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0 80rpx; /* 增加底部padding，让白色区域多一些 */
  position: relative;
  width: 100%;
  color: #fff; /* 文字默认白色 */
}

.avatar {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  border: 4rpx solid #fff; /* UI图中有白色边框 */
  margin-bottom: 20rpx;
  background-color: #eee; /* 默认头像背景 */
}

.nickname-large {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #fff; /* 确保昵称是白色 */
}

.signature-small {
  font-size: 28rpx;
  color: #f0f8ff; /* 签名颜色稍浅 */
}

.header-actions {
  position: absolute;
  top: 60rpx; /* 根据状态栏高度调整 */
  right: 30rpx;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1); /* UI中是带透明度的背景 */
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  /* 如果有多个图标，可以在这里加margin */
}

/* 信息列表 */
.info-list {
  width: 90%;
  background-color: #fff;
  border-radius: 20rpx;
  margin: -40rpx 30rpx 0; /* 向上偏移，产生层叠效果 */
  padding: 0 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
  font-size: 30rpx;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #333;
}

.info-value-container {
  display: flex;
  align-items: center;
  color: #888;
}

.info-value {
  margin-right: 10rpx;
}

.arrow-right {
  width: 24rpx;
  height: 24rpx;
}

/* 个性主题 */
.theme-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 30rpx 30rpx 0;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.theme-list {
  white-space: nowrap; /* 使内部元素横向排列 */
}

.theme-item {
  display: inline-block; /* 使其可以设置宽高并横向排列 */
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  border: 4rpx solid transparent;
  transition: border-color 0.3s;
}

.theme-item:last-child {
  margin-right: 0;
}

.theme-item-active {
  border-color: #007AFF; /* 选中主题的边框颜色，可以替换为UI图中的绿色 */
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  padding: 40rpx 30rpx;
  margin-top: 20rpx;
}

.save-button {
  flex: 1;
  background-color: #28a745; /* UI图中的绿色 */
  color: white;
  border-radius: 40rpx;
  margin-right: 20rpx;
  font-size: 32rpx;
}

.reset-button {
  flex: 1;
  background-color: #fff;
  color: #333;
  border: 1rpx solid #ddd;
  border-radius: 40rpx;
  font-size: 32rpx;
}