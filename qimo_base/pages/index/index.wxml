<!--index.wxml-->
<!-- 1.顶部轮播图 swiper -->
<swiper class="swiper-container" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}">
  <block wx:for="{{swiperList}}" wx:key="id">
    <swiper-item class="swiper-item">
      <!-- <view class="swiper-item-view" style="background-color: {{item.color}};">{{item.text}}</view> -->
      <image class="swiper-item-view" src="{{item.url}}"></image>
    </swiper-item>
  </block>
</swiper>

<!-- 2.横向滚动区 scroll-view -->
<view class="scroll-section-title">热门项目</view>
<scroll-view class="scroll-view_H" scroll-x="true" style="width: 100%">
  <block wx:for="{{scrollHorizontalList}}" wx:key="index">
    <view class="scroll-view-item_H">
      <!-- <view class="scroll-item-image-placeholder" style="background-color: {{item.color}};">
      </view> -->
      <image class="scroll-item-image-placeholder" src="{{item.url}}"></image>
      <!-- <view class="scroll-item-text">{{item.text}}</view> -->
      <view class="scroll-item-text">热门项目{{index + 1}}</view>
    </view>
  </block>
</scroll-view>

<!-- 3.快捷功能入口 icon-->
<view class="quick-functions-container">
  <block wx:for="{{quickFunctions}}" wx:key="id">
    <view class="quick-function-item" catchtap="handleQuickFunctionTap" data-name="{{item.name}}">
      <view class="quick-function-icon-wrapper" style="background-color: {{item.iconBgColor}};">
        <!-- 使用微信原生 icon 组件 -->
        <icon type="{{item.iconType}}" size="23" color="{{item.iconColor}}"/>
      </view>
      <text class="quick-function-text">{{item.name}}</text>
    </view>
  </block>
</view>

<!-- 4.新闻公告区 rich-text 显示全部公告 -->
<view class="announcement-section">
  <view class="announcement-title-container">
    <text class="announcement-title">最新公告</text>
    <view class="announcement-indicator"></view> <!-- UI图标题旁边的小红点 -->
  </view>
  <view class="announcements-list">
    <block wx:for="{{announcementNodes}}" wx:key="index">
      <view class="announcement-item" bind:tap="handleClickAnnouncement">
        <rich-text nodes="{{[item]}}"></rich-text> 
      </view>
    </block>
  </view>
</view>

 <!-- 5.可复制文本区 text-->
<view class="copyable-text-section">
  <text class="copyable-text" selectable="true" bindtap="handleCopyText">{{copyableTextContent}}</text>
</view>