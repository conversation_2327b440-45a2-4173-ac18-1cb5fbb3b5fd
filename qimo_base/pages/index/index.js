Page({

  /**
   * 页面的初始数据
   */
  data: {
    swiperList: [
      { id: 1, color: '#FFB6C1', text: '轮播图1' },
      { id: 2, color: '#ADD8E6', text: '轮播图2' },
      { id: 3, color: '#90EE90', text: '轮播图3' }
    ],
    scrollHorizontalList: [
      { id: 1, text: '热门项目1', color: '#DDA0DD' }, // 紫色系
      { id: 2, text: '热门项目2', color: '#87CEFA' }, // 淡蓝色
      { id: 3, text: '热门项目3', color: '#F0E68C' }, // 卡其色
      { id: 4, text: '热门项目4', color: '#98FB98' }  // 淡绿色
    ],
    quickFunctions: [
      { id: 1, name: '功能1', iconType: 'success_no_circle', iconColor: '#FFFFFF', iconBgColor: '#32CD32', iconClass: 'if-success' }, // 绿色背景，白色图标 (模拟)
      { id: 2, name: '功能2', iconType: 'info', iconColor: '#FFFFFF', iconBgColor: '#1E90FF', iconClass: 'if-info' }, // 蓝色背景
      { id: 3, name: '功能3', iconType: 'warn', iconColor: '#FFFFFF', iconBgColor: '#FF6347', iconClass: 'if-warn' },   // 红色背景
      { id: 4, name: '功能4', iconType: 'waiting', iconColor: '#FFFFFF', iconBgColor: '#87CEEB', iconClass: 'if-time' } // 淡蓝色背景
    ],
    announcementNodes: [
      {
        name: 'div',
        attrs: { class: 'anno-item', style: 'line-height: 1.6; margin-bottom: 10px;' },
        children: [
          { type: 'text', text: '教育部发布新政策，将加强中小学' },
          { 
            name: 'span',
            attrs: { style: 'color: #FF6347; font-weight: bold;' }, // 红色加粗
            children: [{ type: 'text', text: '科技创新教育' }]
          }
        ]
      },
      {
        name: 'div',
        attrs: { class: 'anno-item', style: 'line-height: 1.6; margin-bottom: 10px;' },
        children: [
          { type: 'text', text: '医疗健康领域新发现，或将改变' },
          { 
            name: 'span',
            attrs: { style: 'color: #1E90FF;' }, // 蓝色
            children: [{ type: 'text', text: '传统治疗方式' }]
          }
        ]
      },
      {
        name: 'div',
        attrs: { class: 'anno-item', style: 'line-height: 1.6;' },
        children: [
          { type: 'text', text: '文化交流活动周启动，展示各地' },
          { 
            name: 'span',
            attrs: { style: 'color: #32CD32; font-size: 1.1em;' }, // 绿色，稍大字体
            children: [{ type: 'text', text: '特色文化' }]
          }
        ]
      }
    ],
    copyableTextContent: "这是一段可以点击复制的文本内容！"
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取轮播图
    this.onGetBanner() // 暂时注释掉原有的banner获取，使用静态数据
    // 获取滑动图片
    // this.onGetSwiper()
  },
  handleClickAnnouncement() {
    wx.showModal({
      title: '新闻详情',
      content: '文化交流活动周启动，展示各地特色文化',
      showCancel: false
    })
  },
  async onGetBanner() { // 暂时注释掉，避免与静态数据冲突
    wx.request({
      url: 'https://api.thecatapi.com/v1/images/search?limit=4',
      method: 'get',
      success: (res) => {
        console.log('res', res)
        if (res.statusCode === 200 && res.data.length) {
          this.setData({
            swiperList: res.data,
            scrollHorizontalList: res.data
          })
        }
      },
      fail: (err) => {
        console.log('err', err)
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    
  },

  handleQuickFunctionTap: function(event) {
    const functionName = event.currentTarget.dataset.name;
    wx.showToast({
      title: '点击了：' + functionName,
      icon: 'none', // 不显示默认图标
      duration: 1500
    });
  },

  handleCopyText: function() {
    wx.setClipboardData({
      data: this.data.copyableTextContent,
      success: function (res) {
        wx.showToast({
          title: '文本已复制',
          icon: 'success',
          duration: 1500
        });
      }
    });
  }
})