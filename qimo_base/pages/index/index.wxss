.swiper-container {
  width: 100%;
  height: 300rpx; /* 根据UI图调整高度 */
  border-radius: 20rpx;
  overflow: hidden; /* 确保圆角生效 */
  margin-bottom: 20rpx;
}

.swiper-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-item-view {
  width: 95%; /* légèrement plus petit que swiper-item pour un effet de bordure/marge si nécessaire */
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: white;
  border-radius: 20rpx; /* Appliquer le border-radius ici aussi si l'image/contenu doit être arrondi */
}

/* 横向滚动区样式 */
.scroll-section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  padding-left: 10rpx; /* 根据UI图，标题有少量左边距 */
}

.scroll-view_H {
  white-space: nowrap; /* 关键，使内部元素横向排列不换行 */
  margin-bottom: 20rpx;
}

.scroll-view-item_H {
  display: inline-flex; /* 改为inline-flex，使其外部表现为行内元素，内部为flex容器 */
  flex-direction: column; /* 内部元素垂直排列 */
  width: 280rpx; /* 根据UI图调整宽度 */
  height: 200rpx; /* 根据UI图调整高度 */
  margin-right: 20rpx;
  background-color: #f0f0f0; /* 项目背景色，如果图片未加载 */
  overflow: hidden;
}

.scroll-view-item_H:first-child {
  margin-left: 10rpx; /* 给第一个元素左边距，使其不紧贴边缘 */
}

.scroll-item-image-placeholder {
  width: 100%;
  border-radius: 15rpx;
  height: 140rpx; /* 图片占位符高度 */
  /* background-color set by inline style */
}

.scroll-item-text {
  padding: 10rpx;
  font-size: 24rpx;
  text-align: center;
  color: #333;
  background-color: #ffffff; /* 文本区域背景，使其与图片区分 */
  flex-grow: 1; /* 填充剩余空间 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 快捷功能入口样式 */
.quick-functions-container {
  display: flex;
  justify-content: space-around; /* 均匀分布各项 */
  align-items: center;
  padding: 20rpx 10rpx;
  background-color: #FFFFFF; /* 根据UI图，此区域有白色背景板 */
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
  margin: 20rpx 0; /* 上下边距 */
}

.quick-function-item {
  display: flex;
  flex-direction: column; /* 图标在上，文字在下 */
  align-items: center;
  width: 25%; /* 四个功能均分宽度 */
}

.quick-function-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%; /* 圆形背景 */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  /* background-color is set via inline style from JS data */
  /* For icon component usage, this wrapper provides the colored background */
}

/* 如果直接使用 icon 组件, 可以这样设置其内部 icon 的样式 */
.quick-function-item icon {
  font-size: 50rpx; /* icon组件的大小是通过size属性，但wxss的font-size也能影响部分自定义图标组件 */
}

/* 备选：字体图标的通用样式 (如果采用字体图标方案) */
.icon-font {
  font-family: "iconfont"; /* 假设你引入了名为 iconfont 的字体 */
  font-size: 40rpx; /* 调整字体图标大小 */
  /* color is set via inline style from JS data */
}

.quick-function-text {
  font-size: 24rpx;
  color: #555555;
}

/* 新闻公告区样式 */
.announcement-section {
  background-color: #FFFFFF;
  padding: 20rpx;
  border-radius: 15rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.announcement-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.announcement-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 10rpx;
}

.announcement-indicator {
  width: 12rpx;
  height: 12rpx;
  background-color: #FF6347; /* 小红点颜色 */
  border-radius: 50%;
}

.announcement-content-wrapper {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
}

/* rich-text内部元素的特定类样式 (如果需要) */
.anno-item {
  /* 可以在这里添加更多样式，或者直接在JS的nodes中定义style */
  /* 例如：padding-bottom: 5px; */
}

/* 可复制文本区样式 */
.copyable-text-section {
  background-color: #f0f0f0; /* 根据UI图，背景色较浅 */
  padding: 20rpx;
  border-radius: 10rpx;
  margin: 20rpx 0;
  text-align: center; /* 文本居中 */
}

.copyable-text {
  font-size: 28rpx;
  color: #888888; /* 根据UI图，文字颜色较浅 */
  line-height: 1.5;
}

/* 新闻公告轮播样式 */
.announcement-swiper {
  height: 50rpx; /* 设定一个固定的高度，确保垂直轮播正常工作 */
  overflow: hidden; /* 隐藏超出部分 */
}

.announcement-swiper-item {
  display: flex;
  align-items: center; /* 垂直居中内容 */
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 调整 rich-text 内部的 .anno-item 样式以适应轮播 */
.announcement-swiper-item .anno-item {
  width: 100%;
  line-height: 50rpx !important;   /* 与 swiper 高度一致，实现单行文本垂直居中效果 */
  margin-bottom: 0 !important;    /* 移除原有的 margin-bottom */
  overflow: hidden;               /* 隐藏超出部分 */
  white-space: nowrap;            /* 强制不换行 */
  text-overflow: ellipsis;        /* 超出部分显示省略号 */
  display: block; /* 确保 ellipsis 生效 */
}

/* 确保 rich-text 内的直接子元素（如 text 节点或 span）能配合 nowrap 和 ellipsis */
.announcement-swiper-item .anno-item,
.announcement-swiper-item .anno-item  { /* 应用到所有子元素上，以确保文本不换行 */
  white-space: nowrap;
  display: inline; /* 尝试让所有子元素都在一行，这样 text-overflow: ellipsis 能在 .anno-item 上生效 */
}

/* 特殊处理一下 span，因为 rich-text 的 span 默认可能是 inline */
.announcement-swiper-item .anno-item span {
    display: inline; /* 保持 inline，但 white-space: nowrap 会被继承 */
}

/* 为了让 ellipsis 在 .anno-item 上生效，其子元素不能破坏单行流 */
/* 覆盖 rich-text 默认子节点样式，确保它们不会导致换行或溢出问题 */
.announcement-swiper-item .anno-item div,
.announcement-swiper-item .anno-item p {
    display: inline; /* 强制所有块级子元素变为行内 */
}
