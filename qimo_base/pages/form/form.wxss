/* pages/form/form.wxss */
.Container {
  padding: 20rpx 30rpx;
  background-color: #f4f4f4;
  min-height: 100vh;
  box-sizing: border-box;
}

.Header {
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.Title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 10rpx;
}

.Subtitle {
  display: block;
  font-size: 26rpx;
  color: #888888;
  text-align: center;
}

.QuestionBlock {
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.QuestionTitle {
  display: block;
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.RadioGroup,
.CheckboxGroup {
  display: flex;
  flex-direction: column;
}

.RadioItem,
.CheckboxItem {
  background-color: #f9f9f9;
  padding: 25rpx 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #555555;
}

.RadioItem:last-child,
.CheckboxItem:last-child {
  margin-bottom: 0;
}

.RadioItem radio,
.CheckboxItem checkbox {
  margin-right: 20rpx;
  transform: scale(0.9);
}

/* 微信小程序 radio 和 checkbox 默认样式调整可能有限，这里仅做基础布局 */

.SliderContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.SliderControl {
  flex-grow: 1;
  margin: 0 30rpx;
}

.SliderLabel {
  font-size: 28rpx;
  color: #666666;
}

.TextareaControl {
  width: 100%;
  min-height: 150rpx; /* 初始高度 */
  padding: 20rpx;
  border: 1rpx solid #dddddd;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  background-color: #f9f9f9;
}

.ButtonGroup {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  padding-bottom: 40rpx; /* 避免内容被底部导航遮挡 (如果存在) */
}

.SubmitButton,
.ResetButton {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
  font-weight: 500;
}

.SubmitButton {
  background-color: #1aad19; /* 微信绿 */
  color: #ffffff;
  margin-right: 20rpx;
}

.ResetButton {
  background-color: #e0e0e0;
  color: #333333;
  margin-left: 20rpx;
}