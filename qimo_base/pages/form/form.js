// pages/form/form.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    frequencyItems: [
      { value: 'daily', name: '每天使用' },
      { value: 'weekly', name: '每周几次' },
      { value: 'monthly', name: '每月几次' },
      { value: 'occasionally', name: '偶尔使用' }
    ],
    mostUsedFeatureItems: [
      { value: 'music', name: '听音乐' },
      { value: 'social', name: '社交互动' },
      { value: 'chat', name: '在线聊天' },
      { value: 'imageEditing', name: '图片编辑' },
      { value: 'videoEditing', name: '视频剪辑' }
    ],
    likedAspectsItems: [
      { value: 'interface', name: '界面美观' },
      { value: 'experience', name: '体验流畅' },
      { value: 'functions', name: '功能齐全' },
      { value: 'performance', name: '性能稳定' }
    ],
    expectedFeaturesItems: [
        { value: 'socialInteraction', name: '社交互动' },
        { value: 'onlineChat', name: '在线聊天' },
        { value: 'imageEditing', name: '图片编辑' },
        { value: 'videoEditing', name: '视频剪辑' }
    ],
    formData: { // 用于存储表单数据，特别是slider这种可能不会直接通过event.detail.value获取最新值的场景
      frequency: '', // 新增：用于存储使用频率
      mostUsedFeature: '', // 新增：用于存储最常用功能
      likedAspects: [], // 新增：用于存储喜欢本产品的哪些方面
      satisfaction: 5 // slider 默认值
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化时，确保 form.wxml 中的 likedAspectsItems 与用户期望增加的功能对应
    // 如果 form.wxml 中的问题3 "您喜欢我们小程序的哪些方面？" 的选项
    // 应该与UI稿中"期望增加的功能"一致，则需要修改 this.data.likedAspectsItems
    // 为与 this.data.expectedFeaturesItems 一致的内容。
    // 为简单起见，假设 form.wxml 的 likedAspectsItems 已包含"社交互动", "在线聊天"等。
    // 如果不一致，需要在这里或 data 中调整。
    // 例如，可以这样做，让 form.wxml 的选项与期望功能一致：
    this.setData({
        likedAspectsItems: this.data.expectedFeaturesItems
    });
  },

  handleSubmit(event) {
    const formData = event.detail.value;
    // 表单验证逻辑
    if (!formData.frequency) {
      wx.showToast({
        title: '请选择使用频率',
        icon: 'none'
      });
      return;
    }

    if (!formData.mostUsedFeature) {
      wx.showToast({
        title: '请选择最常用功能',
        icon: 'none'
      });
      return;
    }

    // 根据 onLoad 中的逻辑，likedAspects 实际上对应的是"期望增加的功能"
    if (!formData.likedAspects || formData.likedAspects.length === 0) {
      wx.showToast({
        title: '请选择期望增加的功能',
        icon: 'none'
      });
      return;
    }

    // 假设表单中有一个名为 'suggestion' 的输入框或文本域用于用户建议
    // 如果您的表单中没有此字段，可以移除或注释掉以下验证
    if (!formData.suggestions || formData.suggestions.trim() === '') {
      wx.showToast({
        title: '请输入您的建议',
        icon: 'none'
      });
      return;
    }

    // 添加提交时间
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    formData.submissionTime = `${year}-${month}-${day} ${hours}:${minutes}`;

    // 准备传递给成功页面的数据
    const navigationData = {
      formData: formData,
      frequencyItems: this.data.frequencyItems,
      mostUsedFeatureItems: this.data.mostUsedFeatureItems,
      expectedFeaturesSourceItems: this.data.expectedFeaturesItems 
    };

    wx.navigateTo({
      url: '/pages/form-success/form-success?data=' + encodeURIComponent(JSON.stringify(navigationData))
    });
  },

  handleReset() {
    wx.showToast({
      title: '已重置',
      icon: 'none'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})