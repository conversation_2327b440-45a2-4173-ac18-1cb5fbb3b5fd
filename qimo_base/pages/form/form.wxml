<!--pages/form/form.wxml-->
<view class="Container">
  <view class="Header">
    <text class="Title">用户体验调查问卷</text>
    <text class="Subtitle">感谢您参与本次调查，您的反馈对我们很重要</text>
  </view>
  <form bindsubmit="handleSubmit" bindreset="handleReset">
    <!-- 问题1: 使用频率 (单选) -->
    <view class="QuestionBlock">
      <text class="QuestionTitle">1. 您使用我们的小程序的频率是？</text>
      <radio-group name="frequency" class="RadioGroup">
        <label class="RadioItem" wx:for="{{frequencyItems}}" wx:key="value">
          <radio value="{{item.value}}"/>
          <text>{{item.name}}</text>
        </label>
      </radio-group>
    </view>

    <!-- 问题2: 最常用功能 (单选) -->
    <view class="QuestionBlock">
      <text class="QuestionTitle">2. 您最常使用小程序的哪个功能？</text>
      <radio-group name="mostUsedFeature" class="RadioGroup">
        <label class="RadioItem" wx:for="{{mostUsedFeatureItems}}" wx:key="value">
          <radio value="{{item.value}}"/>
          <text>{{item.name}}</text>
        </label>
      </radio-group>
    </view>

    <!-- 问题3: 喜欢小程序的方面 (多选) -->
    <view class="QuestionBlock">
      <text class="QuestionTitle">3. 您喜欢我们小程序的哪些方面？ (可多选)</text>
      <checkbox-group name="likedAspects" class="CheckboxGroup">
        <label class="CheckboxItem" wx:for="{{likedAspectsItems}}" wx:key="value">
          <checkbox value="{{item.value}}"/>
          <text>{{item.name}}</text>
        </label>
      </checkbox-group>
    </view>

    <!-- 问题4: 整体满意度 (滑动评分) -->
    <view class="QuestionBlock">
      <text class="QuestionTitle">4. 您对我们小程序的整体满意度如何？ (0-10分)</text>
      <view class="SliderContainer">
        <text class="SliderLabel">0</text>
        <slider name="satisfaction" min="0" max="10" step="1" class="SliderControl"/>
        <text class="SliderLabel">10</text>
      </view>
    </view>

    <!-- 问题5: 开放性建议 (文本域) -->
    <view class="QuestionBlock">
      <text class="QuestionTitle">5. 您对我们的小程序有什么宝贵的建议或意见吗？</text>
      <textarea name="suggestions" placeholder="请在此处输入您的建议或意见" class="TextareaControl" maxlength="-1" auto-height="true"/>
    </view>

    <!-- 操作按钮 -->
    <view class="ButtonGroup">
      <button form-type="submit" class="SubmitButton">提交问卷</button>
      <button form-type="reset" class="ResetButton">重置答案</button>
    </view>
  </form>
</view>