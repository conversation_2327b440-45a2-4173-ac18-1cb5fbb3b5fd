/* pages/media/cat-list/cat-list.wxss */
.container {
  padding: 24rpx;
  padding-bottom: 20rpx; /* 为底部加载提示留出空间 */
}

.image-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex-wrap: wrap;
}

.image-item {
  width: 48%;
  height: 200px;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
  border-radius: 10rpx; /* 图片项圆角 */
  overflow: hidden; /* 确保图片圆角生效 */
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1); /* 轻微阴影效果 */
}

.cat-image {
  width: 100%;
  display: block; /* 消除图片下方的空隙 */
}

.loading-indicator {
  width: 100%;
  padding: 20rpx 0;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #888;
}

/* 回到顶部按钮样式 */
.back-to-top {
  position: fixed;
  bottom: 100rpx; /* 离底部距离，可根据实际TabBar或操作栏调整 */
  right: 40rpx;  /* 离右边距离 */
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
  border-radius: 50%; /* 圆形 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999; /* 确保在最上层 */
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.back-to-top .arrow {
  color: #fff;
  font-size: 40rpx; /* 箭头大小 */
  font-weight: bold;
}