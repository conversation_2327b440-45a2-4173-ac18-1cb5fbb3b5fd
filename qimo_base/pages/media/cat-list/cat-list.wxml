<!--pages/media/cat-list/cat-list.wxml-->
<view class="container">
  <view class="image-list">
    <view wx:for="{{picImages}}" wx:key="id" class="image-item">
      <image src="{{item.url}}" bind:tap="handleLookPic" data-item="{{item}}" class="cat-image" lazy-load="true"></image>
    </view>
  </view>
  <view class="loading-indicator">
    <view wx:if="{{isLoading && picImages.length > 0}}" class="loading-text">正在加载中...</view>
    <view wx:if="{{!hasMoreData && picImages.length > 0}}" class="loading-text">没有更多数据了</view>
  </view>

  <!-- 回到顶部按钮 -->
  <view wx:if="{{showBackToTopButton}}" class="back-to-top" bind:tap="goTop">
    <text class="arrow">↑</text>
  </view>
</view>