// pages/media/cat-list/cat-list.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    limit: 10,
    picImages: [],
    page: 1, // 当前页码
    isLoading: false, // 是否正在加载
    hasMoreData: true, // 是否还有更多数据
    showBackToTopButton: false // 控制回到顶部按钮的显示
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.handleGetPicList(true); // 初始加载时视为刷新
  },

  async handleGetPicList(isRefresh = false) {
    if (this.data.isLoading) {
      return;
    }
    this.setData({ isLoading: true });

    wx.request({
      url: `https://api.thecatapi.com/v1/images/search?limit=${this.data.limit}&page=${this.data.page}`,
      method: 'GET',
      success: (res) => {
        console.log('res', res);
        if (res.statusCode === 200) {
          const newImages = res.data;
          if (isRefresh) {
            this.setData({
              picImages: newImages,
              hasMoreData: newImages.length === this.data.limit
            });
          } else {
            this.setData({
              picImages: this.data.picImages.concat(newImages),
              hasMoreData: newImages.length === this.data.limit
            });
          }
        } else {
          // 可以加入错误提示
          this.setData({ hasMoreData: false }); // 请求失败也认为没有更多数据或给出提示
        }
      },
      fail: (err) => {
        console.log('err', err);
        // 可以加入错误提示
        this.setData({ hasMoreData: false }); // 请求失败也认为没有更多数据或给出提示
      },
      complete: () => {
        this.setData({ isLoading: false });
        if (isRefresh) {
          wx.stopPullDownRefresh();
        }
      }
    });
  },
  handleLookPic(e) {
    console.log('e', e)
    const url = e.currentTarget.dataset.item.url
    wx.previewImage({
      urls: [url],
    })
  },

  // 页面滚动监听
  onPageScroll(e) {
    const scrollTop = e.scrollTop;
    const showButton = scrollTop > 300; // 滚动超过300px时显示按钮
    if (showButton !== this.data.showBackToTopButton) {
      this.setData({
        showBackToTopButton: showButton
      });
    }
  },

  // 回到顶部
  goTop: function (e) {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300 // 滚动动画时长
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({ page: 1 });
    this.handleGetPicList(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.isLoading && this.data.hasMoreData) {
      this.setData({ page: this.data.page + 1 });
      this.handleGetPicList(false);
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})