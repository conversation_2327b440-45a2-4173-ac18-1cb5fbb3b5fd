// pages/media/cat/cat.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    picImage: ""
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.onGetCatPic()
  },
  async onGetCatPic() {
    wx.request({
      url: 'https://api.thecatapi.com/v1/images/search',
      method: 'GET',
      success: (res) => {
        console.log('res', res)
        if (res.statusCode === 200 && res.data.length) {
          this.setData({
            picImage: res.data[0].url
          })
        }
      },
      fail: (err) => {
        console.log('err', err)
      }
    })
  },
  handleBack() {
    wx.navigateBack()
  },
  handleLookMore() {
    wx.navigateTo({
      url: '../cat-list/cat-list',
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})