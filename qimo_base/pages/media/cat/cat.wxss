/* pages/media/cat/cat.wxss */
page {
  background-color: #f8f8f8; /* 页面背景色，根据UI图微调 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.cat-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 头部区域 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 10rpx; /* 上下20rpx，左右10rpx */
  margin-bottom: 30rpx;
  background-color: #fff; /* 头部背景通常为白色 */
  border-bottom: 1rpx solid #eee; /* 轻微的下边框 */
  border-radius: 10rpx; /* 轻微圆角 */
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.back-button {
  font-size: 30rpx;
  color: #555;
  padding: 10rpx 15rpx; /* 给返回按钮一些点击区域 */
}

/* 图片区域 */
.image-container {
  width: 100%;
  margin-bottom: 40rpx;
  display: flex; /* 用于居中占位符文本 */
  justify-content: center; /* 用于居中占位符文本 */
  align-items: center; /* 用于居中占位符文本 */
  background-color: #fff; /* 图片容器背景 */
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05); /* 轻微阴影 */
  min-height: 400rpx; /* 给一个最小高度，避免空图片时塌陷 */
}

.cat-image {
  width: 100%; /* 图片宽度充满容器 */
  height: 400rpx; /* 固定高度或根据mode调整 */
  border-radius: 10rpx; /* 图片圆角与容器一致 */
  display: block;
}

/* 如果使用占位符文本时的样式 */
.image-placeholder {
  width: 100%;
  height: 400rpx; /* 与图片区域高度一致 */
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #999;
  font-size: 32rpx;
  background-color: #f0f0f0; /* 占位符背景 */
  border-radius: 10rpx;
}


/* 按钮区域 */
.button-group {
  display: flex;
  justify-content: space-between; /* 让按钮分布在两端，中间留空 */
  width: 100%;
}

.btn {
  flex: 1; /* 让按钮平分空间 */
  margin: 0 10rpx; /* 按钮之间的间距 */
  padding: 20rpx 0;
  font-size: 32rpx;
  border-radius: 10rpx;
  text-align: center;
  border: none; /* 移除默认边框 */
  line-height: normal; /* 确保文字垂直居中 */
}

.btn::after {
  border: none; /* 移除微信小程序按钮默认边框 */
}

.change-button {
  background-color: #fff;
  color: #333;
  border: 1rpx solid #ddd; /* 给浅色按钮一个边框 */
}

.change-button:hover {
  background-color: #f7f7f7;
}

.more-button {
  background-color: #1AAD19; /* UI图中的绿色 */
  color: #fff;
}

.more-button:hover {
  background-color: #179815;
}