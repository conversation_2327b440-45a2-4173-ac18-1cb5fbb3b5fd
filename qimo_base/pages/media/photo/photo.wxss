/* pages/media/cat/cat.wxss */
page {
  background-color: #f8f8f8; /* 页面背景色，根据UI图微调 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.photo-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 头部区域 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 10rpx; /* 上下20rpx，左右10rpx */
  margin-bottom: 30rpx;
  background-color: #fff; /* 头部背景通常为白色 */
  border-bottom: 1rpx solid #eee; /* 轻微的下边框 */
  border-radius: 10rpx; /* 轻微圆角 */
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.back-button {
  font-size: 30rpx;
  color: #555;
  padding: 10rpx 15rpx; /* 给返回按钮一些点击区域 */
}

/* 图片区域 */
.image-container {
  width: 100%;
  margin-bottom: 40rpx;
  display: flex; /* 用于居中占位符文本 */
  justify-content: center; /* 用于居中占位符文本 */
  align-items: center; /* 用于居中占位符文本 */
  background-color: #fff; /* 图片容器背景 */
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05); /* 轻微阴影 */
  min-height: 400rpx; /* 给一个最小高度，避免空图片时塌陷 */
}
.take-pictures{
  background: #DADADA;
  margin-bottom: 12px;
}