// pages/media/photo/photo.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    picImage: ""
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.request({
      url: 'https://api.thecatapi.com/v1/images/search',
      method: 'GET',
      success: (res) => {
        console.log('res', res)
        if (res.statusCode === 200 && res.data.length) {
          this.setData({
            picImage: res.data[0].url
          })
        }
      },
      fail: (err) => {
        console.log('err', err)
      }
    })
  },
  handleBack() {
    wx.navigateBack()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  handleTakePicture() {
    wx.chooseImage({
      count: 1, // 默认9
      sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
      success: (res) => {
        // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
        const tempFilePaths = res.tempFilePaths
        if (tempFilePaths && tempFilePaths.length > 0) {
          this.setData({
            picImage: tempFilePaths[0]
          })
        } else {
          console.error("未选择图片或路径为空");
          wx.showToast({
            title: '未获取到图片',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("拍照失败:", err);
        if (err.errMsg === "chooseImage:fail cancel") {
          wx.showToast({
            title: '您取消了拍照',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: '拍照失败',
            icon: 'none'
          });
        }
      }
    })
  },

  handleSavePicture() {
    const that = this;
    if (!this.data.picImage) {
      wx.showToast({
        title: '没有图片可保存',
        icon: 'none'
      });
      return;
    }

    // 检查相册写入权限
    wx.getSetting({
      success(res) {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success() {
              // 用户同意授权
              that.saveImage();
            },
            fail() {
              // 用户拒绝授权
              wx.showModal({
                title: '授权提示',
                content: '您已拒绝授权保存图片到相册，如需使用请打开设置重新授权。',
                showCancel: true,
                cancelText: '取消',
                confirmText: '去设置',
                success(modalRes) {
                  if (modalRes.confirm) {
                    wx.openSetting({
                      success(settingRes) {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          wx.showToast({
                            title: '授权成功',
                            icon: 'success'
                          });
                          that.saveImage();
                        } else {
                          wx.showToast({
                            title: '授权失败',
                            icon: 'none'
                          });
                        }
                      }
                    });
                  }
                }
              });
            }
          });
        } else {
          // 用户已授权
          that.saveImage();
        }
      },
      fail() {
        wx.showToast({
          title: '获取设置失败',
          icon: 'none'
        });
      }
    });
  },

  saveImage() {
    let imagePath = this.data.picImage;

    // 判断是网络图片还是本地临时文件
    if (imagePath.startsWith('http')) {
      wx.showLoading({
        title: '图片下载中...',
      });
      wx.downloadFile({
        url: imagePath,
        success: (res) => {
          wx.hideLoading();
          if (res.statusCode === 200) {
            this.performSave(res.tempFilePath);
          } else {
            wx.showToast({
              title: '图片下载失败',
              icon: 'none'
            });
          }
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: '图片下载失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 本地临时文件路径
      this.performSave(imagePath);
    }
  },

  performSave(filePath) {
    wx.saveImageToPhotosAlbum({
      filePath: filePath,
      success: () => {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error("保存图片失败:", err);
        if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny" || err.errMsg === "saveImageToPhotosAlbum:fail:auth denied") {
          // 实测中，如果在这里才发现 auth deny，前面 getSetting/authorize 的逻辑可能没完全覆盖所有情况
          // 或者用户在授权后又通过系统设置关闭了权限
          wx.showModal({
            title: '保存失败',
            content: '您已拒绝授权保存图片到相册，请前往设置页面开启权限。',
            confirmText: '去设置',
            showCancel: false,
            success(modalRes) {
              if (modalRes.confirm) {
                wx.openSetting({});
              }
            }
          });
        } else if (err.errMsg === "saveImageToPhotosAlbum:fail cancel") {
          wx.showToast({
            title: '取消保存',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }
    });
  }
})