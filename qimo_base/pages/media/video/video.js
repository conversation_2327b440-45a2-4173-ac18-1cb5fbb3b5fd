// pages/media/video/video.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    danmuInput: '',
    danmuList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },
  handleBack() {
    wx.navigateBack()
  },

  bindInput(e) {
    this.setData({
      danmuInput: e.detail.value
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    this.videoContext = wx.createVideoContext('myVideo');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  bindSendDanmu() {
    if (!this.data.danmuInput || !this.data.danmuInput.trim()) {
      // wx.showToast({ title: '弹幕内容不能为空', icon: 'none' }); // 可选提示
      return;
    }
    const colors = ['#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080'];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];

    if (this.videoContext) {
        this.videoContext.sendDanmu({
            text: this.data.danmuInput,
            color: randomColor
        });
    }

    this.setData({
      danmuInput: ''
    });
  },

  videoErrorCallback(e) {
    console.error('Video Error:', e.detail.errMsg);
    wx.showToast({
      title: '视频加载失败',
      icon: 'none'
    });
  },

  bindVideoEnterPictureInPicture() {
    console.log('进入画中画模式');
  },

  bindVideoLeavePictureInPicture() {
    console.log('退出画中画模式');
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})