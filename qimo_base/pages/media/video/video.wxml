<!--pages/media/video/video.wxml-->
<view class="video-container">
  <!-- 头部区域 -->
  <view class="header">
    <text class="title">视频播放</text>
    <text class="back-button" bind:tap="handleBack">返回</text>
  </view>
  <view class="page-section tc">
    <video id="myVideo" src="https://kfxoss-biz.oss-cn-hangzhou.aliyuncs.com/static/frontend/mp-patient/%E5%9B%9B%E7%82%B9%E8%B7%AA%E4%BD%8D%E7%8C%AB%E7%8B%97%E5%BC%8F%E8%85%B0%E9%83%A8%E6%B4%BB%E5%8A%A8%E8%8C%83%E5%9B%B4%E8%AE%AD%E7%BB%83(1).mp4" binderror="videoErrorCallback" danmu-list="{{danmuList}}" enable-danmu danmu-btn show-center-play-btn='{{false}}' show-play-btn="{{true}}" controls picture-in-picture-mode="{{['push', 'pop']}}" bindenterpictureinpicture='bindVideoEnterPictureInPicture' bindleavepictureinpicture='bindVideoLeavePictureInPicture'></video>
    <view class="danmu-controls">
      <input bindinput="bindInput" value="{{danmuInput}}" class="danmu-input" type="text" placeholder="在此处输入弹幕内容" />
      <button bindtap="bindSendDanmu" class="send-danmu-button" type="primary" formType="submit">发送弹幕</button>
    </view>
  </view>
</view>