/* pages/media/video/video.wxss */
page {
  background-color: #f8f8f8; /* 页面背景色，根据UI图微调 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.video-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  box-sizing: border-box;
  min-height: 100vh; /* 新增 */
  background-color: #f4f4f4; /* 修改 */
}

/* 头部区域 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx; /* 修改 */
  margin-bottom: 20rpx; /* 修改 */
  background-color: #fff; /* 头部背景通常为白色 */
  border-bottom: 1rpx solid #eee; /* 轻微的下边框 */
  border-radius: 10rpx; /* 轻微圆角 */
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.back-button {
  font-size: 30rpx;
  color: #555;
  padding: 10rpx 15rpx; /* 给返回按钮一些点击区域 */
}

video {
  width: 100%;
  margin-bottom: 20rpx;
  display: block;
}

.danmu-controls {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.danmu-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
}

.danmu-input::placeholder {
  color: #999;
}

.send-danmu-button {
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #1AAD19;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  outline: none;
  box-shadow: 0 2rpx 6rpx rgba(26, 173, 25, 0.2);
}

.send-danmu-button::after {
  border: none;
}

.send-danmu-button[disabled] {
  background-color: #c1c1c1;
  color: #fefefe;
  box-shadow: none;
}