/* pages/media/media.wxss */
.container {
    padding: 30rpx;
    box-sizing: border-box;
    min-height: 100vh;
    background: #f8f8f8;
  }
  
  /* 功能选择网格 */
  .feature-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30rpx;
    padding: 20rpx 0;
  }
  
  .feature-item {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  }
  
  .feature-icon {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 20rpx;
  }
  
  .feature-text {
    font-size: 28rpx;
    color: #333;
  }
  
  /* 功能内容区 */
  .feature-content {
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    min-height: 80vh;
  }
  
  .feature-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    padding: 0 20rpx;
  }
  
  .feature-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    flex: 1;
  }
  
  .header-actions {
    display: flex;
    align-items: center;
    gap: 20rpx;
  }
  
  .add-local-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    padding: 12rpx 24rpx;
    background: rgba(26, 173, 25, 0.1);
    border-radius: 30rpx;
    font-size: 28rpx;
  }
  
  .add-local-btn text {
    line-height: 32rpx;
  }
  
  .add-icon {
    width: 32rpx;
    height: 32rpx;
    vertical-align: middle;
  }
  
  .back-btn {
    padding: 12rpx 24rpx;
    font-size: 28rpx;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 猫图功能 */
  .cat-image {
    width: 100%;
    height: 600rpx;
    border-radius: 16rpx;
    margin-bottom: 30rpx;
  }
  
  .button-group {
    display: flex;
    gap: 20rpx;
  }
  
  .refresh-btn, .more-btn {
    flex: 1 !important;
  }
  
  /* 音频功能 */
  audio {
    width: 100%;
    margin: 30rpx 0;
  }
  
  /* 视频功能 */
  .video-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
  }
  
  video {
    width: 100%;
    height: 400rpx;
    border-radius: 16rpx;
  }
  
  .danmu-input-area {
    width: 100%;
    display: flex;
    gap: 20rpx;
    padding: 20rpx;
    background: #f8f8f8;
    border-radius: 16rpx;
    box-sizing: border-box;
  }
  
  .danmu-input {
    flex: 1;
    height: 80rpx;
    background: #ffffff;
    border-radius: 40rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
  }
  
  .send-danmu-btn {
    width: 160rpx !important;
    height: 80rpx;
    line-height: 80rpx;
    background: #1AAD19;
    color: #ffffff;
    border-radius: 40rpx;
    padding: 0;
    font-size: 28rpx;
  }
  
  .send-danmu-btn:active {
    opacity: 0.8;
  }
  
  /* 相机功能 */
  camera {
    width: 100%;
    height: 800rpx;
    border-radius: 16rpx;
    margin-bottom: 30rpx;
  }
  
  .camera-buttons {
    display: flex;
    gap: 20rpx;
    margin-top: 30rpx;
  }
  
  .camera-buttons button {
    flex: 1;
  }
  
  .audio-section {
    margin-bottom: 40rpx;
    padding: 20rpx;
    background: #f8f8f8;
    border-radius: 16rpx;
  }
  
  .media-section {
    margin-top: 40rpx;
  }
  
  .button-group {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30rpx;
  }
  
  .button-group button {
    width: 45%;
  }
  
  .media-preview {
    width: 100%;
    min-height: 400rpx;
    margin-top: 30rpx;
    background: #f8f8f8;
    border-radius: 16rpx;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .media-preview image,
  .media-preview video {
    width: 100%;
    max-height: 600rpx;
  }
  
  /* 音乐播放器 */
  .music-player {
    position: relative;
    min-height: calc(100vh - 200rpx);
    border-radius: 30rpx;
    overflow: hidden;
    transition: background 0.5s ease;
  }
  
  .player-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }
  
  .player-content {
    position: relative;
    z-index: 1;
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: calc(100vh - 260rpx);
  }
  
  .player-main {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    transition: opacity 0.3s ease;
  }
  
  .player-main.hide {
    opacity: 0;
    pointer-events: none;
  }
  
  .player-disc {
    position: relative;
    width: 500rpx;
    height: 500rpx;
    margin: 60rpx 0;
    flex-shrink: 0;
  }
  
  .disc-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    animation: rotate 20s linear infinite;
    animation-play-state: paused;
    filter: drop-shadow(0 0 10rpx rgba(0,0,0,0.3));
  }
  
  .disc-cover {
    position: absolute;
    width: 320rpx;
    height: 320rpx;
    border-radius: 50%;
    top: 90rpx;
    left: 90rpx;
    animation: rotate 20s linear infinite;
    animation-play-state: paused;
    box-shadow: 0 0 20rpx rgba(0,0,0,0.2);
  }
  
  .player-needle {
    position: absolute;
    width: 200rpx;
    height: 300rpx;
    top: -60rpx;
    left: 230rpx;
    transform-origin: 15rpx 15rpx;
    transform: rotate(-25deg);
    transition: transform 0.5s ease;
    z-index: 1;
    filter: drop-shadow(0 0 5rpx rgba(0,0,0,0.3));
  }
  
  .playing .disc-bg,
  .playing .disc-cover {
    animation-play-state: running;
  }
  
  .playing .player-needle {
    transform: rotate(0deg);
  }
  
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  .song-info {
    width: 100%;
    text-align: center;
    margin: 30rpx 0;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
    flex-shrink: 0;
  }
  
  .song-name {
    font-size: 36rpx;
    font-weight: bold;
    display: block;
    margin-bottom: 10rpx;
    padding: 0 40rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .song-author {
    font-size: 28rpx;
    opacity: 0.8;
    display: block;
    padding: 0 40rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .progress-bar {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    box-sizing: border-box;
    margin: 30rpx 0;
    flex-shrink: 0;
  }
  
  .progress-slider {
    flex: 1;
    margin: 0 20rpx;
  }
  
  .time-text {
    font-size: 24rpx;
    color: rgba(255,255,255,0.8);
  }
  
  .player-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 60rpx;
    margin-top: 30rpx;
    width: 100%;
    flex-shrink: 0;
  }
  
  .control-btn {
    width: 80rpx;
    height: 80rpx;
    filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.2));
  }
  
  .play-btn {
    width: 100rpx;
    height: 100rpx;
  }
  
  /* 播放列表相关样式 */
  .playlist-toggle {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx 0;
    color: #ffffff;
    font-size: 28rpx;
    gap: 10rpx;
  }
  
  .toggle-icon {
    font-size: 24rpx;
    transition: transform 0.3s ease;
  }
  
  .playlist-panel {
    position: absolute;
    top: 100rpx;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 30rpx;
    padding: 30rpx;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 2;
  }
  
  .playlist-panel.show {
    transform: translateY(0);
  }
  
  .playlist-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-radius: 15rpx;
    margin-bottom: 20rpx;
    transition: background-color 0.3s ease;
  }
  
  .playlist-item:active {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  .playlist-item.active {
    background-color: rgba(26, 173, 25, 0.1);
  }
  
  .item-cover {
    width: 80rpx;
    height: 80rpx;
    border-radius: 10rpx;
    margin-right: 20rpx;
  }
  
  .item-info {
    flex: 1;
    overflow: hidden;
  }
  
  .item-name {
    font-size: 28rpx;
    color: #333;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .item-author {
    font-size: 24rpx;
    color: #999;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .item-duration {
    font-size: 24rpx;
    color: #999;
    margin-left: 20rpx;
  } 