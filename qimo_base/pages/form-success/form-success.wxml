<view class="PageContainer">
  <view class="HeaderSection">
    <view class="MainTitle">问卷提交成功</view>
    <view class="Subtitle">感谢您的反馈，以下是您的答案</view>
  </view>

  <view class="DetailsCard">
    <view class="DetailItem">
      <text class="ItemLabel">使用频率</text>
      <text class="ItemValue">{{submissionDetails.frequency}}</text>
    </view>

    <view class="DetailItem">
      <text class="ItemLabel">最常用功能</text>
      <text class="ItemValue">{{submissionDetails.mostUsedFeature}}</text>
    </view>

    <view class="DetailItem">
      <text class="ItemLabel">期望增加的功能</text>
      <view class="FeatureTagsContainer">
        <block wx:if="{{submissionDetails.expectedFeatures && submissionDetails.expectedFeatures.length > 0}}">
          <text wx:for="{{submissionDetails.expectedFeatures}}" wx:key="*this" class="FeatureTag">{{item}}</text>
        </block>
        <text wx:else class="ItemValue">无</text>
      </view>
    </view>

    <view class="DetailItem">
      <text class="ItemLabel">使用体验评分</text>
      <view class="ItemValue">
        <text class="ScoreValue">{{submissionDetails.satisfaction}}</text>
        <text wx:if="{{submissionDetails.satisfaction !== '未评分' && submissionDetails.satisfaction !== '-'}}"> 分</text>
      </view>
    </view>

    <view class="DetailItem SuggestionsItem">
      <text class="ItemLabel">建议和意见</text>
      <text class="ItemValue SuggestionsValue">{{submissionDetails.suggestions}}</text>
    </view>

    <view class="DetailItem">
      <text class="ItemLabel">提交时间</text>
      <text class="ItemValue">{{submissionDetails.submissionTime}}</text>
    </view>
  </view>

  <view class="FooterButtonContainer">
    <button class="ReturnButton" bindtap="handleReturnToForm">返回问卷</button>
  </view>
</view> 