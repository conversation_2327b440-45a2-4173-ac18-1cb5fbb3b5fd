Page({
  data: {
    submissionDetails: {
      frequency: '',
      mostUsedFeature: '',
      expectedFeatures: [], // 对应UI稿的 "期望增加的功能"
      satisfaction: 0,
      suggestions: '',
      submissionTime: ''
    }
  },

  onLoad: function (options) {
    if (options.data) {
      try {
        const navigationData = JSON.parse(decodeURIComponent(options.data));
        const formData = navigationData.formData;
        const frequencyItems = navigationData.frequencyItems || [];
        const mostUsedFeatureItems = navigationData.mostUsedFeatureItems || [];
        // expectedFeaturesSourceItems 包含 {value, name} 对象列表
        const expectedFeaturesSourceItems = navigationData.expectedFeaturesSourceItems || []; 

        const frequencyObj = frequencyItems.find(item => item.value === formData.frequency);
        const mostUsedFeatureObj = mostUsedFeatureItems.find(item => item.value === formData.mostUsedFeature);
        
        let expectedFeaturesDisplay = [];
        if (formData.likedAspects && Array.isArray(formData.likedAspects)) {
            expectedFeaturesDisplay = formData.likedAspects.map(value => {
            const featureObj = expectedFeaturesSourceItems.find(item => item.value === value);
            return featureObj ? featureObj.name : value; // 如果找不到匹配项，直接显示值
          });
        }

        this.setData({
          submissionDetails: {
            frequency: frequencyObj ? frequencyObj.name : (formData.frequency || '未填写'),
            mostUsedFeature: mostUsedFeatureObj ? mostUsedFeatureObj.name : (formData.mostUsedFeature || '未填写'),
            expectedFeatures: expectedFeaturesDisplay,
            satisfaction: formData.satisfaction !== undefined ? formData.satisfaction : '未评分',
            suggestions: formData.suggestions || '无',
            submissionTime: formData.submissionTime || '未知'
          }
        });
      } catch (e) {
        console.error("解析数据失败:", e);
        // 可以设置一个错误状态或提示用户
        this.setData({
          submissionDetails: {
            frequency: '数据加载错误',
            mostUsedFeature: '数据加载错误',
            expectedFeatures: [],
            satisfaction: '-',
            suggestions: '数据加载错误',
            submissionTime: '数据加载错误'
          }
        });
      }
    }
  },

  handleReturnToForm: function () {
    wx.navigateBack();
  }
}); 