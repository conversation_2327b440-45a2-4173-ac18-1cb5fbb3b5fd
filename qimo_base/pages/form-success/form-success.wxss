.PageContainer {
  background-color: #f4f4f4;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.HeaderSection {
  text-align: center;
  margin-bottom: 40rpx;
}

.MainTitle {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.Subtitle {
  font-size: 28rpx;
  color: #888;
}

.DetailsCard {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 40rpx;
}

.DetailItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* 使标签和值在建议很多时顶部对齐 */
  padding: 25rpx 0;
  border-bottom: 1rpx solid #eee;
  font-size: 30rpx;
}

.DetailItem:last-child {
  border-bottom: none;
}

.ItemLabel {
  color: #666;
  margin-right: 20rpx;
  white-space: nowrap; /* 防止标签换行 */
}

.ItemValue {
  color: #333;
  text-align: right;
  flex-grow: 1; /* 允许值内容扩展 */
}

.FeatureTagsContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 15rpx; /* 标签之间的间隙 */
}

.FeatureTag {
  background-color: #f0f0f0;
  color: #555;
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
}

.ScoreValue {
  color: #07c160; /* 绿色 */
  font-weight: bold;
  font-size: 36rpx;
}

.SuggestionsItem {
  align-items: flex-start; /* 确保标签和多行文本顶部对齐 */
}

.SuggestionsValue {
  text-align: right; /* 建议内容靠左对齐 */
  white-space: pre-wrap; /* 保留换行符和空格 */
  word-break: break-word; /* 允许长单词换行 */
}

.FooterButtonContainer {
  margin-top: auto; /* 将按钮推到底部，如果页面内容不足 */
  padding: 20rpx 0;
}

.ReturnButton {
  background-color: #07c160; /* 绿色 */
  color: white;
  font-size: 32rpx;
  border-radius: 50rpx; /* 大圆角 */
  padding: 20rpx 0;
  text-align: center;
  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.2);
}

.ReturnButton:active {
  background-color: #05ab54; /* 点击效果 */
} 