/* 基本重置和主题设置 */
:root {
  --primary-color: #e0e0e0; /* 主要文字颜色 */
  --secondary-color: #a0a0a0; /* 次要文字颜色 */
  --background-color: #121212; /* 深色背景 */
  --surface-color: #1e1e1e; /* 卡片和表层背景 */
  --border-color: #333; /* 边框颜色 */
  --accent-color: #b22222; /* 强调色，如火焰红 */
}

/* 动画定义 */
@keyframes kenburns {
  0% {
    transform: scale(1) translate(0, 0);
  }
  50% {
    transform: scale(1.1) translate(-5px, 5px);
  }
  100% {
    transform: scale(1) translate(0, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  background-color: var(--background-color);
  color: var(--primary-color);
  overflow-x: hidden; /* 防止水平溢出 */
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #ff4500; /* 悬停时更亮的红色 */
}

h1, h2, h3, h4 {
  line-height: 1.2;
  margin-bottom: 1rem;
}

img, .placeholder {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 占位符样式 */
.placeholder {
  background-color: #2a2a2a;
  border: 1px dashed var(--border-color);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: var(--secondary-color);
  font-size: 0.9rem;
  min-height: 150px;
}

/* 通用容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页眉样式 */
.main-header {
  background-color: var(--surface-color);
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.main-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin: 0;
}

/* 导航样式 */
.main-nav .nav-links {
  list-style: none;
  display: flex;
  gap: 20px;
}

.main-nav a {
  font-weight: bold;
}

.menu-toggle {
  display: none; /* 隐藏复选框 */
}

.menu-icon {
  display: none; /* 默认隐藏汉堡图标 */
  cursor: pointer;
  flex-direction: column;
  gap: 5px;
}

.menu-icon span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 3px;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* 响应式导航：移动端 */
@media (max-width: 768px) {
  .main-nav .nav-links {
    display: none;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--surface-color);
    padding: 1rem;
    border-top: 1px solid var(--border-color);
  }

  .menu-icon {
    display: flex;
  }

  .menu-toggle:checked ~ .nav-links {
    display: flex; /* 当复选框被选中时显示菜单 */
  }

  /* 汉堡图标动画 */
  .menu-toggle:checked ~ .menu-icon span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }
  .menu-toggle:checked ~ .menu-icon span:nth-child(2) {
    opacity: 0;
  }
  .menu-toggle:checked ~ .menu-icon span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
  }
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border-radius: 5px;
  font-weight: bold;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.btn-primary {
  background-color: var(--accent-color);
  color: #fff;
}

.btn-primary:hover {
  background-color: #ff4500;
  transform: translateY(-2px);
  color: #fff;
}

/* Hero Section 样式 */
.hero {
  position: relative;
  height: 75vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
  padding: 0 20px;
  overflow: hidden; /* 隐藏 kenburns 效果溢出的部分 */
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("./images/index-banner.jpeg");
  background-size: cover;
  background-position: center;
  animation: kenburns 20s ease-in-out infinite;
  z-index: -2;
}

.hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.8));
  z-index: -1;
}

.hero-content {
  max-width: 800px;
}

.hero-title,
.hero-subtitle,
.hero .btn {
  opacity: 0; /* 动画开始前不可见 */
  animation: fadeInUp 0.8s ease-out forwards;
}

.hero-title {
  font-size: 3.5rem;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 8px rgba(0,0,0,0.7);
  letter-spacing: 1px;
  animation-delay: 0.5s;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
  animation-delay: 0.4s;
}

.hero .btn {
  animation-delay: 0.8s;
}

/* 通用区块样式 */
.section-spacing {
  padding: 60px 0;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: var(--primary-color);
}

/* 新闻网格样式 */
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.news-card {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.card-img-top {
  height: 200px;
  width: 100%;
}

.card-body {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
}

.card-text {
  color: var(--secondary-color);
  flex-grow: 1;
  margin-bottom: 1rem;
}

.card-link {
  color: var(--accent-color);
  font-weight: bold;
}

/* 时间线样式 */
.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

/* 垂直中心线 */
.timeline::after {
  content: '';
  position: absolute;
  width: 4px;
  background-color: var(--accent-color);
  top: 0;
  bottom: 0;
  left: 50%;
  margin-left: -2px;
}

.timeline-item {
  padding: 10px 40px;
  position: relative;
  background-color: inherit;
  width: 50%;
  opacity: 0; /* 动画前隐藏 */
}

/* 为每个项目应用动画和延迟 */
.timeline-item:nth-child(odd) {
  left: 0;
  padding-right: 30px;
  animation: slide-in-left 0.6s ease-out forwards;
}

.timeline-item:nth-child(even) {
  left: 50%;
  padding-left: 30px;
  animation: slide-in-right 0.6s ease-out forwards;
}

.timeline-item:nth-child(1) { animation-delay: 0.2s; }
.timeline-item:nth-child(2) { animation-delay: 0.4s; }
.timeline-item:nth-child(3) { animation-delay: 0.6s; }
.timeline-item:nth-child(4) { animation-delay: 0.8s; }
.timeline-item:nth-child(5) { animation-delay: 1.0s; }

.timeline-item::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  right: -10px;
  background-color: var(--background-color);
  border: 4px solid var(--accent-color);
  top: 28px;
  border-radius: 50%;
  z-index: 1;
  transition: all 0.3s ease;
}

.timeline-item:nth-child(even)::after {
  left: -10px;
}

.timeline-content {
  padding: 20px;
  background-color: var(--surface-color);
  position: relative;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease;
}

.timeline-item:hover .timeline-content {
  transform: scale(1.03);
}

.timeline-item:hover::after {
  transform: scale(1.2);
  background-color: var(--accent-color);
}

.timeline-date {
  font-weight: bold;
  color: var(--accent-color);
}

/* 时间线响应式：移动端 */
@media (max-width: 768px) {
  .timeline::after {
    left: 31px;
  }
  .timeline-item {
    width: 100%;
    padding-left: 70px;
    padding-right: 25px;
    animation: slide-in-right 0.6s ease-out forwards; /* 移动端统一从右侧滑入 */
  }
  .timeline-item:nth-child(odd) {
    left: 0;
    animation-name: slide-in-right; /* 确保覆盖桌面端设置 */
  }
  .timeline-item:nth-child(even) {
    left: 0;
    animation-name: slide-in-right; /* 确保覆盖桌面端设置 */
  }
  .timeline-item::after {
    left: 21px;
  }
}

/* 图片画廊样式 */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.gallery-item {
    height: 250px;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
    opacity: 0.8;
}

/* 页脚样式 */
.main-footer {
  background-color: var(--surface-color);
  color: var(--secondary-color);
  text-align: center;
  padding: 2rem 0;
  margin-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.main-footer .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Hot News Section Styles */
#hot-news .hot-news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #c00; /* A strong red from the screenshot */
  padding: 0.75rem 1.5rem;
  margin-bottom: 1rem;
}

#hot-news .hot-news-title {
  color: #fff;
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0;
}

#hot-news .more-link {
  color: #fff;
  font-size: 0.9rem;
}

#hot-news .hot-news-list {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: var(--surface-color);
  padding: 0.5rem 1.5rem;
  border: 1px solid var(--border-color);
}

#hot-news .hot-news-list li {
  border-bottom: 1px solid var(--border-color);
  opacity: 0; /* Initially hidden for animation */
  animation: fadeInUp 0.5s ease-out forwards;
  transition: transform 0.2s ease-in-out;
}

/* Staggered animation delays */
#hot-news .hot-news-list li:nth-child(1) { animation-delay: 0.1s; }
#hot-news .hot-news-list li:nth-child(2) { animation-delay: 0.2s; }
#hot-news .hot-news-list li:nth-child(3) { animation-delay: 0.3s; }
#hot-news .hot-news-list li:nth-child(4) { animation-delay: 0.4s; }
#hot-news .hot-news-list li:nth-child(5) { animation-delay: 0.5s; }
#hot-news .hot-news-list li:nth-child(6) { animation-delay: 0.6s; }
#hot-news .hot-news-list li:nth-child(7) { animation-delay: 0.7s; }
#hot-news .hot-news-list li:nth-child(8) { animation-delay: 0.8s; }

#hot-news .hot-news-list li:last-child {
  border-bottom: none;
}

#hot-news .hot-news-list li:hover {
    transform: translateX(10px);
}

#hot-news .hot-news-list li a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  color: var(--primary-color);
  transition: background-color 0.2s ease-in-out;
}

#hot-news .hot-news-list li a:hover {
  color: var(--accent-color);
}

#hot-news .news-title {
  flex-grow: 1;
  margin-right: 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#hot-news .news-date {
  color: var(--secondary-color);
  white-space: nowrap;
  font-size: 0.9em;
}

/* Responsive styles for Hot News */
@media (max-width: 768px) {
    #hot-news .news-title {
        white-space: normal;
    }
    #hot-news .hot-news-list li a {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
}

/* New styles for section title links */
.section-title a {
  color: inherit; /* 从父元素h2继承颜色 */
  text-decoration: none;
  transition: color 0.3s ease;
}

.section-title a:hover {
  color: #ff4500; /* 悬停时变为更亮的红色，与其他链接效果一致 */
}
