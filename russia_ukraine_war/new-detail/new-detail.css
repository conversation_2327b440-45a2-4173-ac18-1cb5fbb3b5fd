/* 全局样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  background-color: #f4f4f4;
  color: #333;
}

.container {
  max-width: 80%;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
  animation: fadeIn 0.5s ease-in-out;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 页眉 */
.article-header {
  border-bottom: 2px solid #eee;
  padding-bottom: 20px;
  margin-bottom: 30px;
}

.article-header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
  color: #1a1a1a;
}

.article-meta {
  font-size: 0.9em;
  color: #666;
}

/* 文章内容 */
.main-content h2 {
  font-size: 1.8em;
  color: #333;
  margin-top: 40px;
  border-left: 4px solid #0056b3;
  padding-left: 15px;
}

.main-content p {
  margin-bottom: 20px;
  text-align: justify;
}

a {
  color: #0056b3;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #007bff;
  text-decoration: underline;
}

/* 占位符 */
.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 1.2em;
  margin: 30px auto;
  border-radius: 5px;
  transition: all 0.3s ease-in-out;
}

.placeholder:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transform: translateY(-5px);
}

.placeholder-images {
  display: flex;
  gap: 1rem;
}

.placeholder-images img {
  flex: 1;
  width: 100%;
  object-fit: cover;
  min-width: 0;
  border-radius: 5px;
}


#gallery-2 {
  height: 350px;
}

.placeholder-video {
  height: 450px;
}

.placeholder-video video {
  width: 100%;
  height: 100%;
  border-radius: 5px;
}

/* 评论区 */
.comments-section {
  margin-top: 50px;
  padding-top: 30px;
  border-top: 2px solid #eee;
}

.comments-section h2 {
  font-size: 2em;
  margin-bottom: 30px;
}

.comment {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  padding: 15px 20px;
  margin-bottom: 15px;
  border-radius: 5px;
  transition: all 0.3s ease-in-out;
}

.comment:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transform: translateY(-3px);
}

.comment-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  color: #666;
  font-size: 0.9em;
}

.comment-author {
  font-weight: bold;
  color: #0056b3;
}

.comment-body {
  font-size: 1em;
}

.comment-actions {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.comment-actions button {
  background: none;
  border: 1px solid #ccc;
  border-radius: 20px;
  padding: 5px 12px;
  margin-right: 10px;
  cursor: pointer;
  font-size: 0.85em;
  color: #555;
  transition: all 0.2s ease;
}

.comment-actions button:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.like-button.liked {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
  cursor: default;
}

.like-button.liked:hover {
    background-color: #0056b3;
}

.delete-button:hover {
  background-color: #dc3545;
  color: #fff;
  border-color: #dc3545;
}

/* 评论表单 */
.comment-form {
  margin-top: 40px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #e9ecef;
}

.comment-form h3 {
  font-size: 1.5em;
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.comment-form input[type="text"],
.comment-form textarea {
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  box-sizing: border-box; /* 确保 padding 不会影响宽度 */
  font-family: inherit;
  font-size: 1em;
}

.comment-form button[type="submit"] {
  background-color: #0056b3;
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.3s ease;
}

.comment-form button[type="submit"]:hover {
  background-color: #004494;
}

/* 页脚 */
footer {
    text-align: center;
    padding: 20px;
    margin-top: 40px;
    font-size: 0.8em;
    color: #888;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .article-header h1 {
    font-size: 2em;
  }
  .main-content h2 {
    font-size: 1.5em;
  }
  .placeholder-images {
    flex-direction: column;
  }

  #gallery-1,
  #gallery-2 {
    height: auto;
  }

  .placeholder-video {
      height: auto;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 15px;
  }
  .article-header h1 {
    font-size: 1.8em;
  }
  .comment-meta {
      flex-direction: column;
      align-items: flex-start;
  }
  .comment-date {
      margin-top: 5px;
  }
}



