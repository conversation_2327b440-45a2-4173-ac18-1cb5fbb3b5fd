:root {
  --bg-color: #1a1a1a;
  --text-color: #e0e0e0;
  --container-bg: #242424;
  --border-color: #363636;
  --hover-bg: #2f2f2f;
  --rank-1-color: #f56c6c;
  --rank-2-color: #e6a23c;
  --rank-3-color: #f9d854;
  --rank-default-color: #909399;
  --tag-new-bg: #f56c6c;
  --tag-hot-bg: #e6a23c;
  --tag-boil-bg: #f97341;
  --tag-top-bg: #67c23a;
  --link-color: #409eff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.main-grid {
  display: grid;
  grid-template-columns: 1fr 4fr 1.5fr;
  max-width: 1200px;
  margin: 20px auto;
  gap: 20px;
}

.hot-focus-container {
  background-color: var(--container-bg);
  border-radius: 8px;
  padding: 20px;
}

h1 {
  font-size: 24px;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.hot-list {
  list-style: none;
  counter-reset: hot-list-counter;
}

.hot-item {
  display: flex;
  align-items: center;
  padding: 12px 10px;
  border-bottom: 1px solid var(--border-color);
  gap: 15px;
}

.hot-item:last-child {
  border-bottom: none;
}

.hot-list .hot-item::before {
  counter-increment: hot-list-counter;
  content: counter(hot-list-counter);
  font-size: 18px;
  font-weight: bold;
  color: var(--rank-default-color);
  width: 20px;
  text-align: center;
}

.hot-list .hot-item:nth-child(1)::before {
  color: var(--rank-1-color);
}

.hot-list .hot-item:nth-child(2)::before {
  color: var(--rank-2-color);
}

.hot-list .hot-item:nth-child(3)::before {
  color: var(--rank-3-color);
}

.tag.top {
  background-color: var(--tag-top-bg);
}

.title {
  flex-grow: 1;
  font-size: 16px;
}

.score {
  font-size: 14px;
  color: var(--rank-default-color);
  margin-left: auto;
  padding-left: 15px;
}

.tag {
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
  flex-shrink: 0;
}

.tag.new {
  background-color: var(--tag-new-bg);
}

.tag.hot {
  background-color: var(--tag-hot-bg);
}

.tag.boil {
  background-color: var(--tag-boil-bg);
}

.hot-item:hover {
  background-color: var(--hover-bg);
  cursor: pointer;
}

.view-more-btn {
  display: block;
  width: 100%;
  text-align: center;
  padding: 12px;
  margin-top: 10px;
  background-color: var(--hover-bg);
  border-radius: 8px;
  color: var(--text-color);
  text-decoration: none;
  transition: background-color 0.3s;
}

.view-more-btn:hover {
  background-color: #3c3c3c;
}

.sidebar .sidebar-module {
  background-color: var(--container-bg);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.sidebar h3 {
  font-size: 18px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar ul {
  list-style: none;
}

.sidebar ul li {
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.5;
}

.image-placeholder {
  width: 100%;
  height: 120px;
  background-color: #333;
  border-radius: 4px;
  margin-bottom: 10px;
}

.right-sidebar p {
  font-size: 14px;
  color: var(--rank-default-color);
  margin-bottom: 10px;
}

.right-sidebar ul a {
  color: var(--link-color);
  text-decoration: none;
}

.right-sidebar ul a:hover {
  text-decoration: underline;
}

@media (max-width: 1024px) {
  .main-grid {
    grid-template-columns: 1fr;
    max-width: 100%;
    margin: 0;
    gap: 0;
  }
  
  .hot-focus-container {
    border-radius: 0;
    margin-bottom: 20px;
  }

  .sidebar {
    display: none;
  }
}

@media (max-width: 600px) {
  .hot-focus-container {
    padding: 15px;
  }

  h1 {
    font-size: 20px;
  }

  .title {
    font-size: 14px;
  }

  .score {
    font-size: 12px;
  }

  .hot-item {
    gap: 10px;
  }
}
