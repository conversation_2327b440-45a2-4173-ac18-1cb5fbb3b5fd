.back-to-home {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 15px;
  border-radius: 50px;
  text-decoration: none;
  font-size: 14px;
  z-index: 1000;
  transition: background-color 0.3s, transform 0.3s;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.back-to-home:hover {
  background-color: rgba(0, 0, 0, 0.9);
  transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
  .back-to-home {
    bottom: 15px;
    right: 15px;
    padding: 8px 12px;
    font-size: 12px;
  }
} 