/* --- 全局样式和变量 --- */
:root {
  --bg-color: #121212;
  --surface-color: #1e1e1e;
  --primary-text-color: #e0e0e0;
  --secondary-text-color: #a0a0a0;
  --accent-color: #b22222;
  --accent-color-darker: #9d1e1e;
  --border-color: #333;
  --shadow-color: rgba(0, 0, 0, 0.5);

  --font-title: 'Montser<PERSON>', 'Noto Sans SC', sans-serif;
  --font-body: 'Noto Sans SC', sans-serif;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: var(--font-body);
  background-color: var(--bg-color);
  color: var(--primary-text-color);
  line-height: 1.6;
}

* {
  box-sizing: border-box;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1, h2, h3, h4 {
  font-family: var(--font-title);
  font-weight: 700;
  color: var(--accent-color);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--accent-color-darker);
}

img {
  max-width: 100%;
  height: auto;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 50px;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
}

/* --- 图片/视频占位符 --- */
.hero-image-placeholder, .analysis-image-placeholder, .equipment-image-placeholder {
    background-color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--secondary-text-color);
    font-size: 1.2rem;
    border: 1px dashed var(--border-color);
}


/* --- 头部和导航 --- */
.main-header {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: rgba(26, 26, 26, 0.85);
  backdrop-filter: blur(10px);
  padding: 15px 0;
  z-index: 1000;
  border-bottom: 1px solid var(--border-color);
}

.main-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.site-title {
  font-size: 1.8rem;
  margin: 0;
}

.main-nav ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  gap: 25px;
}

.main-nav a {
  font-weight: 700;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}


/* --- 头条新闻 (Hero) --- */
.hero {
  padding: 60px 0;
}

.hero-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: center;
}

.hero-image-placeholder {
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 宽高比 */
  position: relative;
}

.hero-image-placeholder video {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.hero-title {
  font-size: 3rem;
  line-height: 1.2;
  margin: 0 0 20px;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: var(--primary-text-color);
  margin-bottom: 30px;
}

.btn {
  background-color: var(--accent-color);
  color: var(--bg-color);
  padding: 12px 25px;
  font-weight: 700;
  text-transform: uppercase;
  display: inline-block;
  border-radius: 5px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.btn:hover {
  background-color: var(--accent-color-darker);
  transform: translateY(-3px);
  color: var(--bg-color);
}


/* --- 战场装备展示 --- */
.equipment-showcase {
  padding: 60px 0;
  background-color: var(--surface-color);
}

.showcase-container {
  overflow-x: auto;
  /* 为滚动条添加一些美化，同时保持兼容性 */
  scrollbar-width: thin;
  scrollbar-color: var(--accent-color) var(--bg-color);
  padding-bottom: 20px; /* 为滚动条留出空间 */
}

/* 针对 Webkit 浏览器的滚动条样式 */
.showcase-container::-webkit-scrollbar {
    height: 8px;
}

.showcase-container::-webkit-scrollbar-track {
    background: var(--bg-color);
    border-radius: 4px;
}

.showcase-container::-webkit-scrollbar-thumb {
    background-color: var(--accent-color);
    border-radius: 4px;
}

.showcase-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent-color-darker);
}

.showcase-scroll {
  display: flex;
  gap: 20px;
  width: max-content; /* 允许内容超出容器宽度 */
}

.equipment-card {
  width: 280px; /* 固定卡片宽度 */
  background-color: var(--bg-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  flex-shrink: 0; /* 防止卡片在flex容器中被压缩 */
}

.equipment-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px var(--shadow-color);
}

.equipment-image-placeholder {
  background-color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--secondary-text-color);
  font-size: 1.2rem;
  border-bottom: 3px solid var(--accent-color);
  height: 180px;
}

.equipment-content {
  padding: 15px;
}

.equipment-content h4 {
  font-size: 1.2rem;
  margin: 0 0 10px;
  color: var(--primary-text-color);
}

.equipment-content p {
  font-size: 0.9rem;
  color: var(--secondary-text-color);
  margin: 0;
}


/* --- 深度分析 --- */
.analysis-section {
    padding: 60px 0;
}

.analysis-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
    margin-bottom: 80px;
}

.analysis-item.reverse {
    grid-template-columns: 1fr 1fr;
}

.analysis-item.reverse .analysis-image-placeholder {
    order: 2;
}

.analysis-image-placeholder {
    width: 100%;
    height: 400px;
    border-radius: 8px;
}

.analysis-text h3 {
    font-size: 2rem;
    margin-bottom: 20px;
}

.analysis-text p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    color: var(--secondary-text-color);
}

.btn-secondary {
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
    padding: 10px 20px;
    font-weight: 700;
    border-radius: 5px;
    transition: background-color 0.3s, color 0.3s;
}

.btn-secondary:hover {
    background-color: transparent;
    color: var(--primary-text-color);
}


/* --- 各方声音 --- */
.opinions-section {
    padding: 60px 0;
    background-color: var(--surface-color);
}

.opinions-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.opinion-card {
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 25px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.opinion-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

.opinion-avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--secondary-text-color);
    font-size: 0.9rem;
    border: 2px solid var(--accent-color);
    flex-shrink: 0;
}

.author-name {
    margin: 0;
    font-size: 1.2rem;
    color: var(--primary-text-color);
}

.author-title {
    margin: 0;
    font-size: 0.9rem;
    color: var(--secondary-text-color);
}

.opinion-quote {
    margin: 0;
    font-style: italic;
    color: var(--primary-text-color);
    border-left: 3px solid var(--accent-color);
    padding-left: 20px;
    flex-grow: 1;
}

.opinion-quote p {
    margin: 0;
}


/* --- 页脚 --- */
.main-footer-bottom {
  text-align: center;
  padding: 40px 0;
  background-color: var(--bg-color);
  color: var(--secondary-text-color);
  font-size: 0.9rem;
}

.main-footer-bottom p {
  margin: 5px 0;
}


/* --- 响应式设计 --- */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  .hero-image-placeholder {
    margin-bottom: 30px;
    order: -1;
  }
  .analysis-item, .analysis-item.reverse {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
  .analysis-item.reverse .analysis-image-placeholder {
    order: 1;
  }
  .analysis-image-placeholder {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2.2rem;
  }
  .main-header .container {
    flex-direction: column;
    gap: 15px;
  }
  .main-nav ul {
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
  }
  .hero-title {
    font-size: 2.5rem;
  }
  .opinions-grid {
    grid-template-columns: 1fr;
  }
  .analysis-item, .analysis-item.reverse {
    grid-template-columns: 1fr;
  }
  .analysis-item.reverse .analysis-image-placeholder {
    order: -1;
  }
  .analysis-image-placeholder {
    height: 300px;
  }
}
