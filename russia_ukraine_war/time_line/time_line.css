* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  background-color: #f0f2f5;
  color: #333;
  line-height: 1.6;
  margin: 0;
  padding: 20px;
}

header {
  text-align: center;
  margin-bottom: 40px;
}

h1 {
  font-size: 2.5rem;
  color: #1a237e;
  font-weight: 700;
  border-bottom: 3px solid #3f51b5;
  display: inline-block;
  padding-bottom: 10px;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 20px;
}

.main-layout {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 40px;
}

.main-content {
  max-width: 800px; /* This was .event-list's max-width, moving to parent */
  margin: 0 auto;
}

.event-list {
  /* max-width removed from here */
}

.sidebar {
  padding-top: 20px;
}

.sidebar-widget {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.07);
}

.sidebar-widget + .sidebar-widget {
  margin-top: 30px;
}

.widget-title {
  font-size: 1.5rem;
  color: #1a237e;
  margin-top: 0;
  margin-bottom: 20px;
  border-bottom: 2px solid #3f51b5;
  padding-bottom: 10px;
}

.figure-card, .impact-card {
  margin-bottom: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: fadeInUp 0.8s ease-out forwards;
  animation-delay: calc(0.1s * var(--i, 1)); /* Staggered animation */
}

.figure-card:hover, .impact-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.figure-card:last-child, .impact-card:last-child {
  margin-bottom: 0;
}

.figure-card h5, .impact-card h5 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  color: #333;
}

.figure-card p, .impact-card p {
  margin: 0;
  font-size: 0.95rem;
  color: #555;
  line-height: 1.5;
}

.event-item {
  opacity: 0;
  animation: fadeInUp 0.8s ease-out forwards;
  animation-delay: calc(0.1s * var(--i, 1)); /* Staggered animation */
  margin-bottom: 20px;
}

.event-content {
  padding: 25px 30px;
  background-color: #ffffff;
  border-radius: 8px;
  border-left: 5px solid #3f51b5;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.event-content:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.event-content:hover .media-placeholder img {
  transform: scale(1.1);
}

.event-date {
  display: inline-block;
  font-size: 0.9em;
  font-weight: 600;
  color: #ffffff;
  background-color: #3f51b5;
  padding: 5px 15px;
  border-radius: 20px;
  margin-bottom: 15px;
  position: relative;
  overflow: hidden;
}

.event-title {
  margin-top: 0;
  font-size: 1.5rem;
  color: #1a237e;
  font-weight: 600;
}

.media-placeholder {
  background-color: #e9ecef;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #adb5bd;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
}

.media-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* To ensure the image covers the area, might crop */
  transition: transform 0.4s ease-out; /* Smooth transition for zoom */
}

.media-placeholder span {
  display: none; /* Hide the original text */
  transform: translateY(0);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 768px) {
  h1 {
    font-size: 2rem;
  }

  .event-list {
    gap: 20px;
  }

  .event-content {
    padding: 20px;
  }

  .event-title {
    font-size: 1.3rem;
  }
}

/* This rule is redundant and less specific than .event-content:hover */
/*
.event-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
*/

@media (max-width: 1200px) {
  .main-layout {
    grid-template-columns: 1fr;
  }

  .sidebar {
    padding-top: 0;
  }
  
  .left-sidebar {
    order: 2;
  }

  .main-content {
    order: 1;
  }

  .right-sidebar {
    order: 3;
  }
}

@media (max-width: 768px) {
  body {
    // ... existing code ...
  }
  h1 {
    font-size: 2rem;
  }
}

.page-footer {
  text-align: center;
  padding: 20px;
  margin-top: 40px;
  background-color: #e9ecef;
  color: #6c757d;
  font-size: 0.9rem;
  border-top: 1px solid #dee2e6;
}

.page-footer p {
  margin: 0;
}
