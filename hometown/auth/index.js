document.addEventListener('DOMContentLoaded', () => {
  const loginFormContainer = document.getElementById('login-form-container');
  const registerFormContainer = document.getElementById('register-form-container');
  const showRegisterLink = document.getElementById('show-register');
  const showLoginLink = document.getElementById('show-login');

  const loginForm = document.getElementById('login-form');
  const registerForm = document.getElementById('register-form');

  showRegisterLink.addEventListener('click', (event) => {
    event.preventDefault();
    loginFormContainer.style.display = 'none';
    registerFormContainer.style.display = 'block';
  });

  showLoginLink.addEventListener('click', (event) => {
    event.preventDefault();
    registerFormContainer.style.display = 'none';
    loginFormContainer.style.display = 'block';
  });

  loginForm.addEventListener('submit', (event) => {
    event.preventDefault();
    // 在实际应用中，这里会进行AJAX请求到后端进行验证
    alert('登录成功！即将跳转到首页。'); // 模拟登录成功
    // loginForm.reset(); // 清空表单, 跳转前不清空以便用户看到输入
    window.location.href = '../index.html'; // 跳转到首页
  });

  registerForm.addEventListener('submit', (event) => {
    event.preventDefault();
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm-password').value;

    if (password !== confirmPassword) {
      alert('两次输入的密码不一致！');
      return;
    }
    // 在实际应用中，这里会进行AJAX请求到后端进行注册
    alert('注册成功！即将跳转到登录页面。'); // 模拟注册成功
    registerForm.reset(); // 清空表单
    // 注册成功后切换到登录表单
    registerFormContainer.style.display = 'none';
    loginFormContainer.style.display = 'block';
  });
});
