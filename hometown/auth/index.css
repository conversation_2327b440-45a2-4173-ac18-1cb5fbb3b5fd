body {
  font-family: 'Arial', sans-serif;
  background-color: #f4f7f6;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  color: #333;
}

.container {
  background-color: #ffffff;
  padding: 30px 40px;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  box-sizing: border-box;
}

.form-container h2 {
  text-align: center;
  color: #333;
  margin-bottom: 25px;
  font-weight: 600;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.input-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-sizing: border-box;
  font-size: 16px;
  transition: border-color 0.3s;
}

.input-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
  width: 100%;
  padding: 12px;
  background-color: #007bff;
  border: none;
  border-radius: 5px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 10px;
}

.btn:hover {
  background-color: #0056b3;
}

.form-container p {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #555;
}

.form-container p a {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.form-container p a:hover {
  text-decoration: underline;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .container {
    margin: 20px;
    padding: 20px;
  }

  .form-container h2 {
    font-size: 24px;
  }

  .input-group input {
    font-size: 14px;
  }

  .btn {
    font-size: 14px;
  }
}
