:root {
  --primary-color: #C0392B; /* 暗红 - 运城相关色彩参考 */
  --secondary-color: #F39C12; /* 橙黄 - 运城相关色彩参考 */
  --text-color: #333333;
  --light-text-color: #FFFFFF;
  --bg-color: #f8f8f8;
  --card-bg-color: #FFFFFF;
  --border-color: #e0e0e0;
  --footer-bg-color: #333333;
  --placeholder-bg: #e0e0e0;
  --placeholder-text: #777;

  --font-sans-serif: 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --font-serif: 'Georgia', 'Times New Roman', 'Songti SC', serif;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans-serif);
  margin: 0;
  padding: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  font-size: 16px;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.75em;
  font-weight: 600;
}

h1 {
  font-size: 2.5em; /* 示例 */
}

h2 {
  font-size: 2em; /* 示例 */
  margin-bottom: 1em;
  padding-bottom: 0.3em;
  border-bottom: 2px solid var(--primary-color);
  display: inline-block; /* 让边框只包裹文字长度 */
}

h3 {
  font-size: 1.5em; /* 示例 */
  color: var(--primary-color);
}

p {
  margin-bottom: 1em;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover,
a:focus {
  text-decoration: underline;
  color: darkred;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

.page-container {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
}

.page-container-section {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 20px 15px;
}

.image-placeholder {
  background-color: var(--placeholder-bg);
  border: 1px solid var(--border-color);
  color: var(--placeholder-text);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 0.9em;
}

.image-placeholder::before {
  content: attr(data-text);
}

.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

/* Header and Navigation Styles */
header {
  background-color: var(--card-bg-color);
  padding-top: 10px;
  padding-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000; /* Ensure header stays on top */
}

header .page-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header h1 {
  margin: 0;
  font-size: 1.8em; /* Adjusted for header */
  color: var(--primary-color);
}

#main-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
}

#main-nav > ul > li {
  position: relative; /* For submenu positioning */
}

#main-nav ul li a {
  display: block;
  padding: 15px 20px;
  color: var(--text-color);
  text-decoration: none;
  transition: background-color 0.3s ease, color 0.3s ease;
}

#main-nav ul li a:hover,
#main-nav ul li a:focus,
#main-nav ul li.active a { /* Assuming an active class might be added later */
  background-color: var(--primary-color);
  color: var(--light-text-color);
}

/* Submenu Styles */
#main-nav .has-submenu ul {
  position: absolute;
  left: 0;
  top: 100%; /* Position directly below the parent */
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  min-width: 200px;
  z-index: 1001;
  visibility: hidden;
  opacity: 0;
  transform: translateY(10px);
  transition: visibility 0s 0.3s, opacity 0.3s ease-out, transform 0.3s ease-out;
}

#main-nav .has-submenu:hover > ul,
#main-nav .has-submenu:focus-within > ul, /* For keyboard navigation */
#main-nav .has-submenu .submenu-visible {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0s; /* Override transition delay for visibility */
}

#main-nav .has-submenu ul li a {
  padding: 10px 15px;
  white-space: nowrap;
}

#main-nav .has-submenu ul li a:hover,
#main-nav .has-submenu ul li a:focus {
  background-color: var(--secondary-color);
  color: var(--light-text-color);
}

#main-nav .has-submenu ul li a:focus {
  background-color: var(--secondary-color);
  color: var(--light-text-color);
}

/* Carousel Styles */
#carousel-section {
  position: relative; /* Added for correct button positioning context */
  margin-bottom: 30px; /* Space below carousel */
  background-color: var(--placeholder-bg); /* Fallback bg if images are slow */
}

.carousel-container {
  position: relative;
  overflow: hidden;
  width: 100%; /* Takes full width of its parent, e.g., #carousel-section or a .page-container inside it */
  height: 50vh; /* Viewport height, adjust as needed, e.g., 400px */
  max-height: 500px; /* Max height constraint */
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.7s ease-in-out;
  z-index: 0; /* Default z-index */
}

.carousel-slide.active-slide {
  opacity: 1;
  z-index: 1; /* Active slide on top */
}

.carousel-slide .image-placeholder {
  width: 100%;
  height: 100%;
  border: none; /* Remove border if it's inside slide */
  font-size: 1.5em;
}

.carousel-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ensures the image covers the slide, cropping as needed */
}

.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2; /* Above slides but below indicators if they overlap */
  background-color: rgba(0, 0, 0, 0.4);
  color: var(--light-text-color);
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 1.5em;
  transition: background-color 0.3s ease;
}

.carousel-control:hover,
.carousel-control:focus {
  background-color: rgba(0, 0, 0, 0.7);
}

.prev-btn {
  left: 15px;
}

.next-btn {
  right: 15px;
}

.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3; /* Above controls */
  display: flex; /* Arrange indicators horizontally */
}

.carousel-indicators span {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin: 0 5px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.carousel-indicators span.active-indicator {
  background-color: var(--light-text-color);
}

/* Content Section Styles */
#city-intro, #attractions, #culture, #food {
  padding-top: 30px; /* Add top padding to separate from carousel or previous section */
  padding-bottom: 30px; /* Add bottom padding */
}

#city-intro p {
  font-size: 1.1em;
  line-height: 1.7;
}

.attraction-card, .food-card {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.attraction-card:hover, .food-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.12);
}

.attraction-card .image-placeholder, .food-card .image-placeholder {
  width: 100%;
  height: 200px; /* Fixed height for card images */
  margin-bottom: 15px;
  border-radius: 6px 6px 0 0; /* Rounded top corners if placeholder is first element */
}

.attraction-card h3, .food-card h3 {
  margin-top: 0; /* Reset if image is above */
  margin-bottom: 10px;
}

.attraction-card p, .food-card p {
  font-size: 0.95em;
  line-height: 1.5;
  color: #555;
}

/* Footer Styles */
footer {
  background-color: var(--footer-bg-color);
  color: var(--light-text-color);
  padding: 30px 0;
  text-align: center;
  margin-top: 40px; /* Space above footer */
}

footer .page-container p {
  margin-bottom: 5px;
  font-size: 0.9em;
}

footer a {
  color: var(--secondary-color); /* Example: use secondary color for links in footer */
}

footer a:hover,
footer a:focus {
  color: var(--secondary-color);
  text-decoration: none;
}

/* Auth links styling */
#main-nav ul .nav-auth-item:first-of-type {
    margin-left: auto;
}

/* #main-nav ul .nav-auth-item + .nav-auth-item {
    margin-left: 15px; 
} */
