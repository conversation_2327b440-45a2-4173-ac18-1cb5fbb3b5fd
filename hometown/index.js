document.addEventListener('DOMContentLoaded', function() {
  const yearSpan = document.getElementById('current-year');
  if (yearSpan) {
    yearSpan.textContent = new Date().getFullYear();
  }

  const navMenuItems = document.querySelectorAll('#main-nav .has-submenu');

  navMenuItems.forEach(item => {
    const submenu = item.querySelector('ul');
    if (submenu) {
      item.addEventListener('mouseenter', () => {
        submenu.classList.add('submenu-visible');
      });
      item.addEventListener('mouseleave', () => {
        submenu.classList.remove('submenu-visible');
      });
    }
    const parentLink = item.querySelector('a');
    if (parentLink && submenu) {
        parentLink.addEventListener('focus', () => {
        });
    }
  });

  const carouselContainer = document.querySelector('.carousel-container');
  const slides = document.querySelectorAll('.carousel-slide');
  const prevButton = document.querySelector('.prev-btn');
  const nextButton = document.querySelector('.next-btn');
  const indicatorsContainer = document.querySelector('.carousel-indicators');

  let currentSlide = 0;
  let autoPlayInterval = null;
  const autoPlayTime = 5000;

  if (slides.length > 0) {
    slides.forEach((slide, index) => {
      const indicator = document.createElement('span');
      indicator.addEventListener('click', () => {
        goToSlide(index);
        resetAutoPlay();
      });
      indicatorsContainer.appendChild(indicator);
    });
    const indicators = indicatorsContainer.querySelectorAll('span');

    function updateCarousel(newIndex) {
      slides[currentSlide].classList.remove('active-slide');
      if (indicators.length > 0) indicators[currentSlide].classList.remove('active-indicator');

      currentSlide = (newIndex + slides.length) % slides.length;

      slides[currentSlide].classList.add('active-slide');
      if (indicators.length > 0) indicators[currentSlide].classList.add('active-indicator');
    }

    function goToSlide(slideIndex) {
      updateCarousel(slideIndex);
    }

    if (prevButton && nextButton) {
      prevButton.addEventListener('click', () => {
        goToSlide(currentSlide - 1);
        resetAutoPlay();
      });

      nextButton.addEventListener('click', () => {
        goToSlide(currentSlide + 1);
        resetAutoPlay();
      });
    }

    function startAutoPlay() {
      if (autoPlayInterval) clearInterval(autoPlayInterval);
      autoPlayInterval = setInterval(() => {
        goToSlide(currentSlide + 1);
      }, autoPlayTime);
    }

    function stopAutoPlay() {
      clearInterval(autoPlayInterval);
    }

    function resetAutoPlay() {
      stopAutoPlay();
      startAutoPlay();
    }

    if (carouselContainer) {
        carouselContainer.addEventListener('mouseenter', stopAutoPlay);
        carouselContainer.addEventListener('mouseleave', startAutoPlay);
    }
    
    let touchStartX = 0;
    let touchEndX = 0;

    if (carouselContainer) {
        carouselContainer.addEventListener('touchstart', (event) => {
            touchStartX = event.changedTouches[0].screenX;
            stopAutoPlay();
        }, { passive: true });

        carouselContainer.addEventListener('touchend', (event) => {
            touchEndX = event.changedTouches[0].screenX;
            handleSwipeGesture();
            startAutoPlay();
        }, { passive: true });
    }

    function handleSwipeGesture() {
        if (touchEndX < touchStartX - 50) {
            goToSlide(currentSlide + 1);
        }
        if (touchEndX > touchStartX + 50) {
            goToSlide(currentSlide - 1);
        }
    }

    goToSlide(0);
    startAutoPlay();
    
  }

});
