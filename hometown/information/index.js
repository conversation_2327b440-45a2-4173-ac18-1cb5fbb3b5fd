document.addEventListener('DOMContentLoaded', () => {
  const navLinks = document.querySelectorAll('nav ul li a');

  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      // 移除其他链接的 active 类
      navLinks.forEach(navLink => navLink.classList.remove('active'));
      // 为当前点击的链接添加 active 类
      this.classList.add('active');

      const href = this.getAttribute('href');
      // 检查是否是内部锚点链接
      if (href && href.startsWith('#')) {
        e.preventDefault(); // 阻止默认锚点跳转行为
        const targetId = href.substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
          const headerOffset = document.querySelector('header').offsetHeight;
          const elementPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
          const offsetPosition = elementPosition - headerOffset;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        }
      }
    });
  });

  // 监听滚动事件，动态更新导航栏的激活状态
  const sections = document.querySelectorAll('.section');
  window.addEventListener('scroll', () => {
    let current = '';
    const headerOffset = document.querySelector('header').offsetHeight;
    
    sections.forEach(section => {
      const sectionTop = section.offsetTop - headerOffset - 50; // 50px 作为偏移量，使得标题在屏幕偏上时就激活
      if (pageYOffset >= sectionTop) {
        current = section.getAttribute('id');
      }
    });

    navLinks.forEach(link => {
      link.classList.remove('active');
      if (link.getAttribute('href').substring(1) === current) {
        link.classList.add('active');
      }
    });
    // 如果滚动到页面顶部，且没有任何section匹配，则默认激活第一个导航项
    if (!current && window.pageYOffset < sections[0].offsetTop - headerOffset - 50) {
        if (navLinks.length > 0) {
            navLinks[0].classList.add('active');
        }
    }
  });
});
