:root {
  --primary-color: #C0392B; /* 新主色调：暗红 */
  --secondary-color: #F8F9FA; /* 辅助色：更浅的灰色背景 */
  --accent-color: #F39C12;  /* 新点缀色：橙黄 */
  --body-bg-color: #F8F9FA; /* 页面背景色：保持浅灰 */
  --text-color: #343A40; /* 主要文字颜色 */
  --light-text-color: #FFFFFF; /*浅色文字，用于深色背景*/
  --border-color: #DEE2E6; /* 边框颜色 */
  --link-color: #B83324; /* 比主色略浅一点的红色，确保对比度 */
  --hover-color: #A02C1F; /* 链接悬停颜色 - 主色的深色版 */
  --card-bg: #FFFFFF; /* 卡片背景：白色 */
  --shadow-color: rgba(0, 0, 0, 0.07); /* 阴影颜色略微调整 */
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  margin: 0;
  padding: 0;
  background-color: var(--body-bg-color); /* 使用新的页面背景色变量 */
  color: var(--text-color);
  line-height: 1.7;
}

.container {
  width: 90%;
  max-width: 1280px; /* 稍微增大最大宽度 */
  margin: 0 auto;
  padding: 25px 15px;
}

header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #A02C1F 100%);
  color: var(--light-text-color);
  padding: 25px 0;
  text-align: center;
  box-shadow: 0 4px 8px var(--shadow-color);
  position: sticky;
  top: 0;
  z-index: 1000;
}

header h1 {
  margin: 0;
  font-size: 2.8em;
  font-weight: 700;
  letter-spacing: 1px;
}

header .subtitle {
    font-size: 1.1em;
    margin-top: 5px;
    color: rgba(255, 255, 255, 0.85);
}

nav ul {
  list-style: none;
  padding: 0;
  margin: 20px 0 0 0;
  text-align: center;
}

nav ul li {
  display: inline-block;
  margin: 0 20px;
}

nav ul li a {
  color: var(--light-text-color);
  text-decoration: none;
  font-size: 1.15em;
  font-weight: 500;
  padding: 8px 15px;
  border-radius: 5px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

nav ul li a:hover, nav ul li a.active {
  background-color: rgba(255,255,255,0.15); /* 调整高亮背景透明度 */
  color: var(--accent-color); /* 使用新的点缀色高亮 */
}

.section {
  background-color: var(--card-bg);
  margin-bottom: 30px;
  padding: 25px 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px var(--shadow-color);
}

.section h2 {
  color: var(--primary-color); /* 标题使用主色 */
  border-bottom: 3px solid var(--primary-color); /* 标题下划线使用主色 */
  padding-bottom: 15px;
  margin-top: 0;
  margin-bottom: 25px;
  font-size: 2em;
  font-weight: 600;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* 调整最小宽度 */
  gap: 25px;
}

.grid-item {
  background-color: var(--body-bg-color); /* 卡片背景使用页面背景色，或保持浅灰 */
  padding: 20px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: transform 0.25s ease-in-out, box-shadow 0.25s ease-in-out;
  display: flex;
  flex-direction: column;
}

.grid-item:hover {
  transform: translateY(-6px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.grid-item h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: var(--primary-color); /* 卡片标题使用主色 */
  font-size: 1.4em;
  font-weight: 600;
}

.grid-item p {
  font-size: 1em;
  margin-bottom: 15px;
  flex-grow: 1; /* 让p元素占据多余空间，将链接推到底部 */
  color: #555;
}

.grid-item a {
  color: var(--link-color);
  text-decoration: none;
  font-weight: 600;
  display: inline-block;
  padding: 8px 12px;
  border: 1px solid var(--link-color);
  border-radius: 5px;
  transition: background-color 0.3s ease, color 0.3s ease;
  align-self: flex-start; /* 链接靠左对齐 */
}

.grid-item a:hover {
  background-color: var(--link-color);
  color: var(--light-text-color);
  text-decoration: none;
}

/* Form styling */
#contact form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

#contact form input[type="text"],
#contact form input[type="email"],
#contact form textarea {
  width: 100%;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  box-sizing: border-box; /* Important for width calculation */
  margin-bottom: 15px;
  font-size: 1em;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

#contact form input[type="text"]:focus,
#contact form input[type="email"]:focus,
#contact form textarea:focus {
  border-color: var(--primary-color); /* 表单聚焦时边框使用主色 */
  box-shadow: 0 0 0 0.2rem rgba(192, 57, 43, 0.25); /* 阴影匹配主色 */
  outline: none;
}

#contact form button[type="submit"] {
  background-color: var(--primary-color); /* 提交按钮使用主色 */
  color: var(--light-text-color);
  padding: 12px 25px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.1em;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

#contact form button[type="submit"]:hover {
  background-color: var(--hover-color); /* 按钮悬停使用链接悬停色 */
}

footer {
  text-align: center;
  padding: 30px 20px;
  background-color: #343A40;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95em;
  margin-top: 30px;
}

footer p {
    margin: 5px 0;
}

footer a {
    color: var(--accent-color); /* 页脚链接使用新的点缀色 */
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 992px) {
    .grid-container {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
  header h1 {
    font-size: 2.2em;
  }
  header .subtitle {
    font-size: 1em;
  }
  nav ul li {
    display: block;
    margin: 10px auto;
  }
  nav ul li a {
    padding: 10px;
  }
  .section {
    padding: 20px;
  }
  .section h2 {
    font-size: 1.8em;
  }
  .grid-item h3 {
    font-size: 1.3em;
  }
}
