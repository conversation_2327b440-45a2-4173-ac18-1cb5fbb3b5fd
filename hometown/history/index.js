document.addEventListener('DOMContentLoaded', () => {
  const historyItems = document.querySelectorAll('.history-item');

  historyItems.forEach(item => {
    item.addEventListener('click', () => {
      const details = item.querySelector('.details');
      if (details) {
        if (details.style.display === 'none' || details.style.display === '') {
          details.style.display = 'block';
        } else {
          details.style.display = 'none';
        }
      }
    });
  });
}); 