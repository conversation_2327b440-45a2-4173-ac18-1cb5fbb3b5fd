:root {
  --primary-color: #C0392B; /* 暗红 - 运城相关色彩参考 */
  --secondary-color: #F39C12; /* 橙黄 - 运城相关色彩参考 */
  --text-color: #333333;
  --light-text-color: #FFFFFF;
  --bg-color: #f8f8f8;
  --card-bg-color: #FFFFFF;
  --border-color: #e0e0e0;
  --footer-bg-color: #333333;
  --placeholder-bg: #e0e0e0;
  --placeholder-text: #777;

  --font-sans-serif: 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --font-serif: 'Georgia', 'Times New Roman', 'Songti SC', serif;
}

body {
  font-family: var(--font-serif); /* 使用CSS变量 */
  margin: 0;
  padding: 0;
  background-color: var(--bg-color); /* 使用CSS变量 */
  color: var(--text-color); /* 使用CSS变量 */
  line-height: 1.7;
}

header {
  background: var(--primary-color); /* 使用CSS变量 */
  color: var(--light-text-color); /* 使用CSS变量 */
  padding: 2.5rem 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-bottom: 3px solid var(--secondary-color); /* 使用CSS变量，强调色 */
}

header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.8rem;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

header .subtitle {
  margin: 0;
  font-size: 1.3rem;
  font-style: italic;
  color: var(--light-text-color); /* 使用CSS变量，可考虑调整为略浅于主白 */
  opacity: 0.9;
  font-weight: 300;
}

main {
  padding: 25px;
  max-width: 960px;
  margin: 30px auto;
  background-color: var(--card-bg-color); /* 使用CSS变量 */
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
}

#history-list {
  list-style: none;
  padding: 0;
}

.history-item {
  background-color: var(--card-bg-color); /* 使用CSS变量 */
  border: 1px solid var(--border-color); /* 使用CSS变量 */
  margin-bottom: 25px;
  padding: 25px;
  border-radius: 10px;
  transition: transform 0.3s ease, box-shadow 0.35s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.history-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 5px;
  height: 100%;
  background-color: var(--secondary-color); /* 使用CSS变量，强调色 */
  opacity: 0;
  transition: opacity 0.3s ease;
}

.history-item:hover::before {
  opacity: 1;
}

.history-item:hover {
  transform: translateY(-6px) translateX(2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.history-item h2 {
  margin-top: 0;
  color: var(--primary-color); /* 使用CSS变量 */
  font-size: 1.9rem;
  margin-bottom: 0.8rem;
}

.history-item p {
  margin-bottom: 12px;
  font-size: 1.05rem;
  color: var(--text-color); /* 使用CSS变量 */
}

.details {
  margin-top: 20px;
  padding: 15px;
  background-color: var(--placeholder-bg); /* 使用CSS变量，或浅一号的背景色 */
  border-left: 5px solid var(--secondary-color); /* 使用CSS变量 */
  font-size: 0.95rem;
  animation: fadeIn 0.5s ease-in-out;
  border-radius: 0 5px 5px 0;
}

.details p {
  color: var(--placeholder-text); /* 使用CSS变量 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

footer {
  text-align: center;
  padding: 25px;
  background-color: var(--footer-bg-color); /* 使用CSS变量 */
  color: var(--light-text-color); /* 使用CSS变量 */
  margin-top: 40px;
  font-size: 0.9rem;
} 