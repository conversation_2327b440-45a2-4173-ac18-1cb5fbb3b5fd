document.addEventListener('DOMContentLoaded', function () {
  // 平滑滚动到锚点
  const navLinks = document.querySelectorAll('nav a');
  navLinks.forEach(link => {
    link.addEventListener('click', function (e) {
      e.preventDefault();
      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        window.scrollTo({
          top: targetElement.offsetTop - 80, // 预留一些空间给固定的header
          behavior: 'smooth'
        });

        // 更新导航链接的 active 状态
        navLinks.forEach(navLink => navLink.classList.remove('active'));
        this.classList.add('active');
      }
    });
  });

  // 返回顶部按钮功能
  const toTopBtn = document.getElementById('to-top-btn');

  window.addEventListener('scroll', function () {
    if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
      toTopBtn.style.display = 'block';
    } else {
      toTopBtn.style.display = 'none';
    }
  });

  toTopBtn.addEventListener('click', function () {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
    // 点击返回顶部后，将第一个导航设为 active
    navLinks.forEach(navLink => navLink.classList.remove('active'));
    if(navLinks.length > 0) {
        navLinks[0].classList.add('active');
    }
  });

  // 页面加载时根据URL锚点设置active的导航链接，并滚动到对应位置
  // 同时处理首次加载时，第一个nav item为active
  function setActiveNavOnLoad() {
    const hash = window.location.hash;
    let activeSet = false;
    if (hash) {
        navLinks.forEach(link => {
            if (link.getAttribute('href') === hash) {
                link.classList.add('active');
                const targetElement = document.getElementById(hash.substring(1));
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'auto' // 初始加载时不需要平滑滚动
                    });
                }
                activeSet = true;
            } else {
                link.classList.remove('active');
            }
        });
    }
    if (!activeSet && navLinks.length > 0) {
        navLinks[0].classList.add('active');
    }
  }

  // 初始加载时设置导航状态
  setActiveNavOnLoad();

  // 监听hash变化，例如用户通过浏览器前进后退按钮
  window.addEventListener('hashchange', setActiveNavOnLoad);

});
