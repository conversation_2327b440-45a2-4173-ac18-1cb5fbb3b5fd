/* 引用全局变量 */
@import url('../index.css');

body {
  font-family: var(--font-sans-serif);
  margin: 0;
  padding: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
}

.container {
  width: 80%;
  margin: auto;
  overflow: hidden;
  padding: 20px;
}

header {
  background: var(--primary-color);
  color: var(--light-text-color);
  padding: 1rem 0;
  text-align: center;
  border-bottom: 5px solid var(--secondary-color);
}

header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-family: var(--font-serif);
}

nav ul {
  padding: 0;
  list-style: none;
  text-align: center;
  background: var(--primary-color);
  margin-top: -5px; /* 与header的border紧密结合 */
}

nav ul li {
  display: inline;
  margin-right: 20px;
}

nav ul li a {
  color: var(--light-text-color);
  text-decoration: none;
  font-size: 1.1rem;
  padding: 5px 10px;
  transition: background-color 0.3s ease;
}

nav ul li a:hover,
nav ul li a.active {
  background-color: var(--secondary-color);
  border-radius: 5px;
}

.section {
  padding: 40px 20px;
  margin-bottom: 20px;
  background-color: var(--card-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section h2 {
  color: var(--primary-color);
  text-align: center;
  font-size: 2rem;
  margin-bottom: 30px;
  font-family: var(--font-serif);
  position: relative;
}

.section h2::after {
  content: '';
  display: block;
  width: 60px;
  height: 3px;
  background-color: var(--secondary-color);
  margin: 10px auto 0;
}

.section h3 {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-top: 20px;
  margin-bottom: 10px;
}

.section p {
  text-align: justify;
  margin-bottom: 15px;
  font-size: 1rem;
}

.image-placeholder {
  background-color: var(--placeholder-bg);
  color: var(--placeholder-text);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border: 2px dashed var(--border-color);
  font-size: 1rem;
  margin: 20px auto; /* 上下20px，左右自动居中 */
}

.gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: space-around; /* 子项均匀分布 */
}

.gallery-item {
  flex-basis: calc(33.333% - 20px); /* 减去gap，一行三个 */
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--bg-color); /* 轻微背景区分 */
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 1px 5px rgba(0,0,0,0.05);
}

.gallery-item .image-placeholder {
  margin-bottom: 10px; /* 图片与文字间距 */
}

.gallery-item h4 {
  color: var(--primary-color);
  font-size: 1.2rem;
  margin-top: 0;
  margin-bottom: 5px;
}

.gallery-item p {
  font-size: 0.9rem;
  text-align: center;
}

/* 特殊模块样式 */
#impression .image-placeholder {
  width: 100%; /* 占满容器宽度 */
  max-width: 800px; /* 最大宽度限制 */
  height: 400px;
}

#about-yuncheng .content-wrapper {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

#about-yuncheng .text-content {
  flex: 2;
}

#about-yuncheng .image-placeholder {
  flex: 1;
  width: 100%; /* 占满 flex item 的宽度 */
  max-width: 400px;
  height: 250px;
  margin-top: 0; /* 与文本对齐 */
}


#modern-yuncheng .image-placeholder {
  width: 100%;
  max-width: 600px;
  height: 350px;
}

footer {
  text-align: center;
  padding: 20px;
  background: var(--footer-bg-color);
  color: var(--light-text-color);
  margin-top: 30px;
}

footer a {
  color: var(--secondary-color);
  text-decoration: none;
}

footer a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    width: 95%;
  }

  header h1 {
    font-size: 2rem;
  }

  nav ul li {
    display: block;
    margin: 10px 0;
  }

  .gallery-item {
    flex-basis: calc(50% - 10px); /* 一行两个 */
  }
   #about-yuncheng .content-wrapper {
    flex-direction: column;
  }
  #about-yuncheng .image-placeholder {
    margin-top: 20px;
    max-width: 100%; /* 移动端图片宽度适应 */
  }
}

@media (max-width: 480px) {
  .gallery-item {
    flex-basis: 100%; /* 一行一个 */
  }
}

/* 返回顶部按钮 */
#to-top-btn {
  display: none;
  position: fixed;
  bottom: 20px;
  right: 30px;
  z-index: 99;
  border: none;
  outline: none;
  background-color: var(--primary-color);
  color: white;
  cursor: pointer;
  padding: 12px 15px;
  border-radius: 50%;
  font-size: 18px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.3);
  transition: background-color 0.3s;
}

#to-top-btn:hover {
  background-color: var(--secondary-color);
}

/* Helper class for full width image placeholders within sections */
.full-width-placeholder {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}
