<template>
	<view class="container">
		<!-- 商品图片 -->
		<view class="product-image-section">
			<view class="product-image" :style="{backgroundColor: product.bgColor}">
				<text class="product-emoji">{{ product.emoji }}</text>
			</view>
		</view>

		<!-- 商品信息 -->
		<view class="product-info-section">
			<view class="product-header">
				<text class="product-name">{{ product.name }}</text>
				<text class="product-price">¥{{ product.price }}</text>
			</view>
			<text class="product-description">{{ product.fullDescription }}</text>
			
			<!-- 规格选择 -->
			<view class="specs-section">
				<text class="specs-title">规格</text>
				<view class="specs-options">
					<view 
						v-for="(spec, index) in specs" 
						:key="index"
						class="spec-item"
						:class="{ active: selectedSpec === index }"
						@click="selectSpec(index)"
					>
						<text class="spec-name">{{ spec.name }}</text>
						<text class="spec-price">+¥{{ spec.extraPrice }}</text>
					</view>
				</view>
			</view>

			<!-- 数量选择 -->
			<view class="quantity-section">
				<text class="quantity-title">数量</text>
				<view class="quantity-controls">
					<view class="quantity-btn" @click="decreaseQuantity">
						<text class="quantity-icon">-</text>
					</view>
					<text class="quantity-number">{{ quantity }}</text>
					<view class="quantity-btn" @click="increaseQuantity">
						<text class="quantity-icon">+</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<view class="total-price">
				<text class="total-label">总计</text>
				<text class="total-amount">¥{{ totalPrice }}</text>
			</view>
			<view class="action-buttons">
				<view class="add-cart-btn" @click="addToCart">
					<text class="btn-text">加入购物车</text>
				</view>
				<view class="buy-now-btn" @click="buyNow">
					<text class="btn-text">立即购买</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'

	// 响应式数据
	const selectedSpec = ref(0)
	const quantity = ref(1)

	// 商品信息
	const product = ref({
		name: '草莓蛋糕',
		price: 68,
		emoji: '🍓',
		bgColor: '#FFE5E5',
		fullDescription: '精选新鲜草莓制作，口感丰富，奶香浓郁。采用进口奶油和优质面粉，每一口都是满满的幸福感。'
	})

	// 规格选项
	const specs = ref([
		{ name: '6寸', extraPrice: 0 },
		{ name: '8寸', extraPrice: 20 },
		{ name: '10寸', extraPrice: 40 }
	])

	// 计算总价
	const totalPrice = computed(() => {
		const basePrice = product.value.price
		const specPrice = specs.value[selectedSpec.value].extraPrice
		return (basePrice + specPrice) * quantity.value
	})

	// 选择规格
	const selectSpec = (index) => {
		selectedSpec.value = index
	}

	// 增加数量
	const increaseQuantity = () => {
		quantity.value++
	}

	// 减少数量
	const decreaseQuantity = () => {
		if (quantity.value > 1) {
			quantity.value--
		}
	}

	// 加入购物车
	const addToCart = () => {
		uni.showToast({
			title: '已加入购物车',
			icon: 'success'
		})
	}

	// 立即购买
	const buyNow = () => {
		uni.showToast({
			title: '跳转到订单页面',
			icon: 'none'
		})
	}

	// 页面加载时获取商品信息
	onLoad((options) => {
		// 这里可以根据传入的商品ID获取商品详情
		console.log('商品ID:', options.id)
	})
</script>

<style scoped>
	.container {
		background-color: #f8f9fa;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	.product-image-section {
		background-color: #fff;
		padding: 40rpx;
		margin-bottom: 20rpx;
	}

	.product-image {
		width: 100%;
		height: 500rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.product-emoji {
		font-size: 120rpx;
	}

	.product-info-section {
		background-color: #fff;
		padding: 40rpx;
		margin-bottom: 20rpx;
	}

	.product-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 20rpx;
	}

	.product-name {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		flex: 1;
	}

	.product-price {
		font-size: 40rpx;
		font-weight: bold;
		color: #ff6b6b;
	}

	.product-description {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 40rpx;
	}

	.specs-section, .quantity-section {
		margin-bottom: 40rpx;
	}

	.specs-title, .quantity-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.specs-options {
		display: flex;
		gap: 20rpx;
	}

	.spec-item {
		flex: 1;
		padding: 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 10rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.spec-item.active {
		border-color: #ff6b6b;
		background-color: #fff5f5;
	}

	.spec-name {
		display: block;
		font-size: 26rpx;
		color: #333;
		margin-bottom: 5rpx;
	}

	.spec-price {
		font-size: 22rpx;
		color: #999;
	}

	.spec-item.active .spec-price {
		color: #ff6b6b;
	}

	.quantity-controls {
		display: flex;
		align-items: center;
		gap: 30rpx;
	}

	.quantity-btn {
		width: 60rpx;
		height: 60rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.quantity-btn:active {
		transform: scale(0.9);
		border-color: #ff6b6b;
	}

	.quantity-icon {
		font-size: 28rpx;
		color: #333;
	}

	.quantity-number {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		min-width: 60rpx;
		text-align: center;
	}

	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid #e0e0e0;
		display: flex;
		align-items: center;
		gap: 30rpx;
	}

	.total-price {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
	}

	.total-label {
		font-size: 22rpx;
		color: #999;
	}

	.total-amount {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff6b6b;
	}

	.action-buttons {
		display: flex;
		gap: 20rpx;
		flex: 1;
	}

	.add-cart-btn, .buy-now-btn {
		flex: 1;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.add-cart-btn {
		background-color: #fff;
		border: 2rpx solid #ff6b6b;
	}

	.add-cart-btn .btn-text {
		color: #ff6b6b;
		font-size: 28rpx;
		font-weight: bold;
	}

	.buy-now-btn {
		background-color: #ff6b6b;
	}

	.buy-now-btn .btn-text {
		color: #fff;
		font-size: 28rpx;
		font-weight: bold;
	}

	.add-cart-btn:active, .buy-now-btn:active {
		transform: scale(0.98);
	}
</style>
