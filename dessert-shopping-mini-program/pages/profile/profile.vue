<template>
  <view class="container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-avatar" @click="handleAvatarClick" v-if="!isLoggedIn">
        <text class="avatar-emoji">👤</text>
      </view>
      <view class="user-avatar" v-else>
        <image style="width: 100%; height: 100%" :src="userInfo.avatar"></image>
      </view>
      <view class="user-info">
        <text class="user-name">{{ displayName }}</text>
        <text class="user-level">{{ userInfo.level }}</text>
      </view>
      <view class="user-points" v-if="isLoggedIn">
        <text class="points-label">积分</text>
        <text class="points-value">{{ userInfo.points }}</text>
      </view>
    </view>

    <!-- 会员卡片 -->
    <view class="member-card">
      <view class="card-header">
        <text class="card-title">会员特权</text>
        <text class="card-emoji">👑</text>
      </view>
      <view class="privileges">
        <view class="privilege-item">
          <text class="privilege-emoji">🎁</text>
          <text class="privilege-text">生日礼品</text>
        </view>
        <view class="privilege-item">
          <text class="privilege-emoji">💰</text>
          <text class="privilege-text">专享折扣</text>
        </view>
        <view class="privilege-item">
          <text class="privilege-emoji">🚚</text>
          <text class="privilege-text">免费配送</text>
        </view>
      </view>
    </view>

    <!-- 订单统计 -->
    <view class="order-stats">
      <view class="stats-header">
        <text class="stats-title">我的订单</text>
        <text class="view-all" @click="viewAllOrders">查看全部 ></text>
      </view>
      <view class="stats-grid">
        <view v-for="(stat, index) in orderStats" :key="index" class="stat-item" @click="goToOrders(stat.type)">
          <text class="stat-emoji">{{ stat.emoji }}</text>
          <text class="stat-count">{{ stat.count }}</text>
          <text class="stat-label">{{ stat.label }}</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view v-for="(menu, index) in menuList" :key="index" class="menu-item" @click="handleMenuClick(menu)">
        <view class="menu-left">
          <text class="menu-emoji">{{ menu.emoji }}</text>
          <text class="menu-title">{{ menu.title }}</text>
        </view>
        <view class="menu-right">
          <text class="menu-badge" v-if="menu.badge">{{ menu.badge }}</text>
          <text class="menu-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 设置菜单 -->
    <view class="settings-section">
      <view v-for="(setting, index) in settingsList" :key="index" class="menu-item" @click="handleSettingClick(setting)">
        <view class="menu-left">
          <text class="menu-emoji">{{ setting.emoji }}</text>
          <text class="menu-title">{{ setting.title }}</text>
        </view>
        <view class="menu-right">
          <text class="menu-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section" v-if="isLoggedIn">
      <view class="logout-btn" @click="logout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>
    <!-- <button @getuserinfo="handleGetUserInfo" open-type="getUserInfo">点击登录</button> -->
    <!-- <button @click="handleGetUserInfo">点击登录</button> -->
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import LoginModal from '@/components/LoginModal.vue';

// 登录状态
const isLoggedIn = ref(false);
const showLoginModal = ref(false);

// 用户信息
const userInfo = ref({
  name: '请登录',
  level: '',
  avatar: '../../static/orange.png',
  points: 1280,
  phone: '',
  isLoggedIn: false
});

// 计算属性 - 显示名称
const displayName = computed(() => {
  return isLoggedIn.value ? userInfo.value.name : '请登录';
});

// 订单统计
const orderStats = ref([
  { emoji: '💳', count: 2, label: '待付款', type: 'unpaid' },
  { emoji: '📦', count: 1, label: '待收货', type: 'shipping' },
  { emoji: '⭐', count: 0, label: '待评价', type: 'review' },
  { emoji: '🔄', count: 0, label: '退换货', type: 'return' }
]);

// 功能菜单
const menuList = ref([
  { emoji: '❤️', title: '我的收藏', badge: '3' },
  { emoji: '🎫', title: '优惠券', badge: '5' },
  { emoji: '📍', title: '收货地址' },
  { emoji: '👥', title: '邀请好友', badge: 'NEW' },
  { emoji: '🎁', title: '积分商城' },
  { emoji: '📞', title: '联系客服' }
]);

// 设置菜单
const settingsList = ref([
  { emoji: '⚙️', title: '设置' },
  { emoji: '❓', title: '帮助与反馈' },
  { emoji: '📋', title: '关于我们' }
]);

// 页面加载时初始化
onMounted(() => {
  initUserInfo();
});

// 初始化用户信息
const initUserInfo = () => {
  try {
    const savedUserInfo = uni.getStorageSync('userInfo');
    if (savedUserInfo && savedUserInfo.isLoggedIn) {
      userInfo.value = savedUserInfo;
      isLoggedIn.value = true;
    } else {
      isLoggedIn.value = false;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    isLoggedIn.value = false;
  }
};

// 点击头像
const handleAvatarClick = () => {
  uni.getUserInfo({
    success: (e) => {
      console.log('e', e);
      const user = e.userInfo;
      userInfo.value.name = user.nickName;
      userInfo.value.avatar = user.avatarUrl;
      userInfo.value.level = 'VIP会员';
      uni.setStorageSync('userInfo', userInfo.value);
      isLoggedIn.value = true;
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      });
    },
    fail: () => {}
  });
};

const handleGetUserInfo = (e) => {
  console.log('e', e);
  uni.getUserInfo({
    success: (e) => {
      console.log('e', e);
      const user = e.userInfo;
      userInfo.value.name = user.nickName;
      userInfo.value.avatar = user.avatarUrl;
      userInfo.value.level = 'VIP会员';
      uni.setStorageSync('userInfo', userInfo.value);
      isLoggedIn.value = true;
    },
    fail: () => {}
  });
};

// 方法
const viewAllOrders = () => {
  if (!isLoggedIn.value) {
    showLoginModal.value = true;
    return;
  }
  uni.showToast({
    title: '跳转到订单列表',
    icon: 'none'
  });
};

const goToOrders = (type) => {
  if (!isLoggedIn.value) {
    showLoginModal.value = true;
    return;
  }
  uni.showToast({
    title: `查看${type}订单`,
    icon: 'none'
  });
};

const handleMenuClick = (menu) => {
  if (!isLoggedIn.value) {
    showLoginModal.value = true;
    return;
  }
  uni.showToast({
    title: `点击了${menu.title}`,
    icon: 'none'
  });
};

const handleSettingClick = (setting) => {
  uni.showToast({
    title: `点击了${setting.title}`,
    icon: 'none'
  });
};

const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除本地存储
        uni.removeStorageSync('userInfo');

        // 重置状态
        isLoggedIn.value = false;
        userInfo.value = {
          name: '甜品爱好者',
          level: 'VIP会员',
          points: 1280,
          phone: '',
          isLoggedIn: false
        };

        uni.showToast({
          title: '退出成功',
          icon: 'success'
        });
      }
    }
  });
};
</script>

<style scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  gap: 30rpx;
  color: #fff;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-emoji {
  font-size: 60rpx;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
}

.user-level {
  font-size: 24rpx;
  opacity: 0.9;
}

.user-points {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5rpx;
}

.points-label {
  font-size: 22rpx;
  opacity: 0.8;
}

.points-value {
  font-size: 28rpx;
  font-weight: bold;
}

/* 会员卡片 */
.member-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.card-emoji {
  font-size: 32rpx;
}

.privileges {
  display: flex;
  justify-content: space-around;
}

.privilege-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.privilege-emoji {
  font-size: 40rpx;
}

.privilege-text {
  font-size: 22rpx;
  color: #666;
}

/* 订单统计 */
.order-stats {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.view-all {
  font-size: 24rpx;
  color: #ff6b6b;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.stat-item:active {
  background-color: #f5f5f5;
  transform: scale(0.95);
}

.stat-emoji {
  font-size: 40rpx;
}

.stat-count {
  font-size: 24rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 菜单样式 */
.menu-section,
.settings-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f5f5f5;
}

.menu-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.menu-emoji {
  font-size: 32rpx;
}

.menu-title {
  font-size: 28rpx;
  color: #333;
}

.menu-right {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.menu-badge {
  background-color: #ff6b6b;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  min-width: 30rpx;
  text-align: center;
}

.menu-arrow {
  font-size: 24rpx;
  color: #ccc;
}

/* 退出登录 */
.logout-section {
  margin: 40rpx 20rpx 20rpx;
}

.logout-btn {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background-color: #f5f5f5;
  transform: scale(0.98);
}

.logout-text {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: bold;
}
</style>
