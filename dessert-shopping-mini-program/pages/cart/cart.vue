<template>
	<view class="container">
		<!-- 购物车为空 -->
		<view v-if="cartItems.length === 0" class="empty-cart">
			<text class="empty-emoji">🛒</text>
			<text class="empty-text">购物车空空如也</text>
			<text class="empty-desc">快去选购你喜欢的甜品吧~</text>
			<view class="go-shopping-btn" @click="goShopping">
				<text class="btn-text">去购物</text>
			</view>
		</view>

		<!-- 购物车商品列表 -->
		<view v-else class="cart-content">
			<view class="cart-header">
				<text class="cart-title">购物车 ({{ totalItems }})</text>
				<text class="clear-btn" @click="clearCart">清空</text>
			</view>

			<view class="cart-list">
				<view 
					v-for="(item, index) in cartItems" 
					:key="index"
					class="cart-item"
				>
					<view class="item-checkbox" @click="toggleSelect(index)">
						<text class="checkbox-icon" :class="{ checked: item.selected }">
							{{ item.selected ? '✓' : '' }}
						</text>
					</view>
					
					<view class="item-image" :style="{backgroundColor: item.bgColor}">
						<text class="item-emoji">{{ item.emoji }}</text>
					</view>
					
					<view class="item-info">
						<text class="item-name">{{ item.name }}</text>
						<text class="item-spec">{{ item.spec }}</text>
						<text class="item-price">¥{{ item.price }}</text>
					</view>
					
					<view class="item-controls">
						<view class="quantity-controls">
							<view class="quantity-btn" @click="decreaseQuantity(index)">
								<text class="quantity-icon">-</text>
							</view>
							<text class="quantity-number">{{ item.quantity }}</text>
							<view class="quantity-btn" @click="increaseQuantity(index)">
								<text class="quantity-icon">+</text>
							</view>
						</view>
						<view class="delete-btn" @click="removeItem(index)">
							<text class="delete-icon">🗑️</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部结算栏 -->
			<view class="bottom-bar">
				<view class="select-all" @click="toggleSelectAll">
					<text class="checkbox-icon" :class="{ checked: isAllSelected }">
						{{ isAllSelected ? '✓' : '' }}
					</text>
					<text class="select-all-text">全选</text>
				</view>
				
				<view class="total-info">
					<text class="total-label">合计:</text>
					<text class="total-price">¥{{ totalPrice }}</text>
				</view>
				
				<view class="checkout-btn" :class="{ disabled: selectedItems.length === 0 }" @click="checkout">
					<text class="btn-text">结算({{ selectedItems.length }})</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue'

	// 购物车商品数据
	const cartItems = ref([
		{
			name: '草莓蛋糕',
			spec: '8寸',
			price: 88,
			quantity: 1,
			emoji: '🍓',
			bgColor: '#FFE5E5',
			selected: true
		},
		{
			name: '巧克力马卡龙',
			spec: '经典装',
			price: 25,
			quantity: 2,
			emoji: '🍫',
			bgColor: '#F5E6D3',
			selected: true
		},
		{
			name: '香草泡芙',
			spec: '单个装',
			price: 18,
			quantity: 3,
			emoji: '🥐',
			bgColor: '#FFF8E1',
			selected: false
		}
	])

	// 计算属性
	const totalItems = computed(() => {
		return cartItems.value.reduce((total, item) => total + item.quantity, 0)
	})

	const selectedItems = computed(() => {
		return cartItems.value.filter(item => item.selected)
	})

	const isAllSelected = computed(() => {
		return cartItems.value.length > 0 && cartItems.value.every(item => item.selected)
	})

	const totalPrice = computed(() => {
		return selectedItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
	})

	// 方法
	const toggleSelect = (index) => {
		cartItems.value[index].selected = !cartItems.value[index].selected
	}

	const toggleSelectAll = () => {
		const selectAll = !isAllSelected.value
		cartItems.value.forEach(item => {
			item.selected = selectAll
		})
	}

	const increaseQuantity = (index) => {
		cartItems.value[index].quantity++
	}

	const decreaseQuantity = (index) => {
		if (cartItems.value[index].quantity > 1) {
			cartItems.value[index].quantity--
		}
	}

	const removeItem = (index) => {
		uni.showModal({
			title: '确认删除',
			content: '确定要删除这个商品吗？',
			success: (res) => {
				if (res.confirm) {
					cartItems.value.splice(index, 1)
				}
			}
		})
	}

	const clearCart = () => {
		uni.showModal({
			title: '确认清空',
			content: '确定要清空购物车吗？',
			success: (res) => {
				if (res.confirm) {
					cartItems.value = []
				}
			}
		})
	}

	const goShopping = () => {
		uni.switchTab({
			url: '/pages/index/index'
		})
	}

	const checkout = () => {
		if (selectedItems.value.length === 0) {
			uni.showToast({
				title: '请选择商品',
				icon: 'none'
			})
			return
		}
		
		uni.showToast({
			title: '跳转到结算页面',
			icon: 'none'
		})
	}
</script>

<style scoped>
	.container {
		background-color: #f8f9fa;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	/* 空购物车样式 */
	.empty-cart {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 200rpx 40rpx;
		text-align: center;
	}

	.empty-emoji {
		font-size: 120rpx;
		margin-bottom: 40rpx;
	}

	.empty-text {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 20rpx;
	}

	.empty-desc {
		font-size: 26rpx;
		color: #999;
		margin-bottom: 60rpx;
	}

	.go-shopping-btn {
		background-color: #ff6b6b;
		padding: 20rpx 60rpx;
		border-radius: 50rpx;
	}

	.go-shopping-btn .btn-text {
		color: #fff;
		font-size: 28rpx;
		font-weight: bold;
	}

	/* 购物车内容样式 */
	.cart-content {
		padding: 20rpx;
	}

	.cart-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		margin-bottom: 20rpx;
	}

	.cart-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.clear-btn {
		font-size: 26rpx;
		color: #ff6b6b;
	}

	.cart-list {
		background-color: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
	}

	.cart-item {
		display: flex;
		align-items: center;
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		gap: 20rpx;
	}

	.cart-item:last-child {
		border-bottom: none;
	}

	.item-checkbox {
		width: 40rpx;
		height: 40rpx;
	}

	.checkbox-icon {
		width: 40rpx;
		height: 40rpx;
		border: 2rpx solid #ddd;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 20rpx;
		color: #fff;
		transition: all 0.3s ease;
	}

	.checkbox-icon.checked {
		background-color: #ff6b6b;
		border-color: #ff6b6b;
	}

	.item-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.item-emoji {
		font-size: 50rpx;
	}

	.item-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.item-name {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
	}

	.item-spec {
		font-size: 22rpx;
		color: #999;
	}

	.item-price {
		font-size: 26rpx;
		color: #ff6b6b;
		font-weight: bold;
	}

	.item-controls {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 20rpx;
	}

	.quantity-controls {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.quantity-btn {
		width: 50rpx;
		height: 50rpx;
		border: 1rpx solid #ddd;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.quantity-icon {
		font-size: 24rpx;
		color: #333;
	}

	.quantity-number {
		font-size: 26rpx;
		color: #333;
		min-width: 40rpx;
		text-align: center;
	}

	.delete-btn {
		font-size: 24rpx;
	}

	/* 底部结算栏 */
	.bottom-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid #e0e0e0;
		display: flex;
		align-items: center;
		gap: 30rpx;
	}

	.select-all {
		display: flex;
		align-items: center;
		gap: 15rpx;
	}

	.select-all-text {
		font-size: 26rpx;
		color: #333;
	}

	.total-info {
		flex: 1;
		text-align: right;
	}

	.total-label {
		font-size: 26rpx;
		color: #333;
	}

	.total-price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff6b6b;
		margin-left: 10rpx;
	}

	.checkout-btn {
		background-color: #ff6b6b;
		padding: 20rpx 40rpx;
		border-radius: 40rpx;
		transition: all 0.3s ease;
	}

	.checkout-btn.disabled {
		background-color: #ccc;
	}

	.checkout-btn .btn-text {
		color: #fff;
		font-size: 26rpx;
		font-weight: bold;
	}
</style>
