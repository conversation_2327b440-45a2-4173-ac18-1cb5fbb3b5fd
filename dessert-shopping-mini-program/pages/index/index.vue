<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input" @click="goToSearch">
				<text class="search-icon">🔍</text>
				<input
					class="search-input-field"
					type="text"
					v-model="searchKeyword"
					placeholder="搜索你喜欢的甜品..."
					@focus="goToSearch"
					@confirm="handleSearch"
				/>
			</view>
		</view>

		<!-- 轮播图 -->
		<view class="banner-section">
			<swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
				<swiper-item v-for="(banner, index) in banners" :key="index">
					<view class="banner-item" :style="{backgroundColor: banner.color}">
						<view class="banner-content">
							<text class="banner-title">{{ banner.title }}</text>
							<text class="banner-subtitle">{{ banner.subtitle }}</text>
						</view>
						<text class="banner-emoji">{{ banner.emoji }}</text>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 分类标签栏 -->
		<view class="category-tabs">
			<scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
				<view class="tabs-container">
					<view
						v-for="(category, index) in categories"
						:key="index"
						class="tab-item"
						:class="{ active: activeTab === index }"
						@click="switchTab(index)"
					>
						<text class="tab-emoji">{{ category.emoji }}</text>
						<text class="tab-name">{{ category.name }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 商品列表 -->
		<view class="products-section">
			<view class="section-header">
				<text class="section-title">{{ categories[activeTab].name }}</text>
				<text class="section-more">查看更多 ></text>
			</view>

			<view class="products-grid">
				<view
					v-for="(product, index) in currentProducts"
					:key="index"
					class="product-card"
					@click="goToProduct(product)"
				>
					<view class="product-image" :style="{backgroundColor: product.bgColor}">
						<text class="product-emoji">{{ product.emoji }}</text>
					</view>
					<view class="product-info">
						<text class="product-name">{{ product.name }}</text>
						<text class="product-desc">{{ product.description }}</text>
						<view class="product-price-row">
							<text class="product-price">¥{{ product.price }}</text>
							<view class="add-btn">
								<text class="add-icon">+</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 推荐区域 -->
		<view class="recommend-section">
			<view class="section-header">
				<text class="section-title">今日推荐</text>
				<text class="section-more">换一批 🔄</text>
			</view>

			<scroll-view class="recommend-scroll" scroll-x="true" show-scrollbar="false">
				<view class="recommend-container">
					<view
						v-for="(item, index) in recommendations"
						:key="index"
						class="recommend-card"
					>
						<view class="recommend-image" :style="{backgroundColor: item.bgColor}">
							<text class="recommend-emoji">{{ item.emoji }}</text>
						</view>
						<text class="recommend-name">{{ item.name }}</text>
						<text class="recommend-price">¥{{ item.price }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue'

	// 响应式数据
	const activeTab = ref(0)
	const searchKeyword = ref('')

	// 轮播图数据
	const banners = ref([
		{
			title: '新品上市',
			subtitle: '精选法式甜品',
			emoji: '🧁',
			color: '#FFE5E5'
		},
		{
			title: '限时优惠',
			subtitle: '买二送一',
			emoji: '🍰',
			color: '#E5F3FF'
		},
		{
			title: '会员专享',
			subtitle: '8折优惠',
			emoji: '🍪',
			color: '#F0FFE5'
		}
	])

	// 分类数据
	const categories = ref([
		{ name: '全部', emoji: '🍽️' },
		{ name: '蛋糕', emoji: '🎂' },
		{ name: '马卡龙', emoji: '🧁' },
		{ name: '泡芙', emoji: '🥐' },
		{ name: '布丁', emoji: '🍮' },
		{ name: '冰淇淋', emoji: '🍦' },
		{ name: '饼干', emoji: '🍪' }
	])

	// 商品数据
	const allProducts = ref({
		0: [ // 全部
			{ name: '草莓蛋糕', description: '新鲜草莓制作', price: 68, emoji: '🍓', bgColor: '#FFE5E5' },
			{ name: '巧克力马卡龙', description: '法式经典', price: 25, emoji: '🍫', bgColor: '#F5E6D3' },
			{ name: '香草泡芙', description: '奶香浓郁', price: 18, emoji: '🥐', bgColor: '#FFF8E1' },
			{ name: '芒果布丁', description: '热带风味', price: 22, emoji: '🥭', bgColor: '#FFF3E0' }
		],
		1: [ // 蛋糕
			{ name: '草莓蛋糕', description: '新鲜草莓制作', price: 68, emoji: '🍓', bgColor: '#FFE5E5' },
			{ name: '巧克力蛋糕', description: '浓郁巧克力', price: 78, emoji: '🍫', bgColor: '#F5E6D3' },
			{ name: '芝士蛋糕', description: '免烤芝士', price: 58, emoji: '🧀', bgColor: '#FFF8E1' },
			{ name: '红丝绒蛋糕', description: '经典美式', price: 88, emoji: '❤️', bgColor: '#FFE5E5' }
		],
		2: [ // 马卡龙
			{ name: '巧克力马卡龙', description: '法式经典', price: 25, emoji: '🍫', bgColor: '#F5E6D3' },
			{ name: '草莓马卡龙', description: '粉嫩可爱', price: 25, emoji: '🍓', bgColor: '#FFE5E5' },
			{ name: '抹茶马卡龙', description: '日式风味', price: 28, emoji: '🍵', bgColor: '#E8F5E8' },
			{ name: '柠檬马卡龙', description: '清香怡人', price: 25, emoji: '🍋', bgColor: '#FFFACD' }
		],
		3: [ // 泡芙
			{ name: '香草泡芙', description: '奶香浓郁', price: 18, emoji: '🥐', bgColor: '#FFF8E1' },
			{ name: '巧克力泡芙', description: '浓郁可可', price: 20, emoji: '🍫', bgColor: '#F5E6D3' },
			{ name: '卡仕达泡芙', description: '经典口味', price: 18, emoji: '🥐', bgColor: '#FFF8E1' },
			{ name: '抹茶泡芙', description: '清香淡雅', price: 22, emoji: '🍵', bgColor: '#E8F5E8' }
		],
		4: [ // 布丁
			{ name: '芒果布丁', description: '热带风味', price: 22, emoji: '🥭', bgColor: '#FFF3E0' },
			{ name: '焦糖布丁', description: '香甜焦糖', price: 20, emoji: '🍮', bgColor: '#F5E6D3' },
			{ name: '抹茶布丁', description: '清香回甘', price: 24, emoji: '🍵', bgColor: '#E8F5E8' },
			{ name: '牛奶布丁', description: '丝滑香甜', price: 18, emoji: '🥛', bgColor: '#F8F8FF' }
		],
		5: [ // 冰淇淋
			{ name: '香草冰淇淋', description: '经典口味', price: 15, emoji: '🍦', bgColor: '#FFF8E1' },
			{ name: '巧克力冰淇淋', description: '浓郁可可', price: 18, emoji: '🍫', bgColor: '#F5E6D3' },
			{ name: '草莓冰淇淋', description: '新鲜果味', price: 18, emoji: '🍓', bgColor: '#FFE5E5' },
			{ name: '抹茶冰淇淋', description: '清香淡雅', price: 20, emoji: '🍵', bgColor: '#E8F5E8' }
		],
		6: [ // 饼干
			{ name: '巧克力饼干', description: '香脆可口', price: 12, emoji: '🍪', bgColor: '#F5E6D3' },
			{ name: '燕麦饼干', description: '健康美味', price: 10, emoji: '🌾', bgColor: '#F5F5DC' },
			{ name: '黄油饼干', description: '奶香浓郁', price: 15, emoji: '🧈', bgColor: '#FFF8E1' },
			{ name: '杏仁饼干', description: '坚果香味', price: 18, emoji: '🥜', bgColor: '#F5E6D3' }
		]
	})

	// 推荐商品
	const recommendations = ref([
		{ name: '提拉米苏', price: 45, emoji: '☕', bgColor: '#F5E6D3' },
		{ name: '舒芙蕾', price: 35, emoji: '🥞', bgColor: '#FFF8E1' },
		{ name: '司康饼', price: 28, emoji: '🥐', bgColor: '#FFE5E5' },
		{ name: '马德琳', price: 22, emoji: '🧁', bgColor: '#E8F5E8' },
		{ name: '费南雪', price: 25, emoji: '🍯', bgColor: '#FFF3E0' }
	])

	// 计算当前分类的商品
	const currentProducts = computed(() => {
		return allProducts.value[activeTab.value] || []
	})

	// 切换标签
	const switchTab = (index) => {
		activeTab.value = index
	}

	// 跳转到商品详情
	const goToProduct = (product) => {
		uni.navigateTo({
			url: `/pages/product/product?id=${product.name}&name=${product.name}&price=${product.price}&emoji=${product.emoji}&bgColor=${encodeURIComponent(product.bgColor)}`
		})
	}

	// 跳转到搜索页面
	const goToSearch = () => {
		uni.navigateTo({
			url: '/pages/search/search'
		})
	}

	// 处理搜索
	const handleSearch = () => {
		if (searchKeyword.value.trim()) {
			uni.navigateTo({
				url: `/pages/search/search?keyword=${encodeURIComponent(searchKeyword.value)}`
			})
		}
	}
</script>

<style scoped>
	.container {
		background-color: #f8f9fa;
		min-height: 100vh;
		padding-bottom: 40rpx;
	}

	/* 搜索栏样式 */
	.search-bar {
		padding: 20rpx 30rpx;
		background-color: #fff;
	}

	.search-input {
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		border-radius: 50rpx;
		padding: 20rpx 30rpx;
		gap: 20rpx;
	}

	.search-icon {
		font-size: 32rpx;
		color: #999;
	}

	.search-input-field {
		font-size: 28rpx;
		color: #333;
		flex: 1;
		background: transparent;
		border: none;
		outline: none;
	}

	.search-input-field::placeholder {
		color: #999;
	}

	/* 轮播图样式 */
	.banner-section {
		padding: 0 30rpx 30rpx;
		background-color: #fff;
	}

	.banner-swiper {
		height: 300rpx;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.banner-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 40rpx;
		height: 100%;
		box-sizing: border-box;
	}

	.banner-content {
		display: flex;
		flex-direction: column;
		gap: 10rpx;
	}

	.banner-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.banner-subtitle {
		font-size: 24rpx;
		color: #666;
	}

	.banner-emoji {
		font-size: 80rpx;
	}

	/* 分类标签栏样式 */
	.category-tabs {
		background-color: #fff;
		padding: 30rpx 0;
		margin-bottom: 20rpx;
	}

	.tabs-scroll {
		white-space: nowrap;
	}

	.tabs-container {
		display: flex;
		padding: 0 30rpx;
		gap: 30rpx;
	}

	.tab-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 10rpx;
		padding: 20rpx;
		border-radius: 20rpx;
		min-width: 120rpx;
		transition: all 0.3s ease;
	}

	.tab-item.active {
		background-color: #ff6b6b;
		transform: scale(1.05);
	}

	.tab-emoji {
		font-size: 40rpx;
	}

	.tab-name {
		font-size: 24rpx;
		color: #333;
		white-space: nowrap;
	}

	.tab-item.active .tab-name {
		color: #fff;
		font-weight: bold;
	}

	/* 商品区域样式 */
	.products-section {
		background-color: #fff;
		margin-bottom: 20rpx;
		border-radius: 20rpx 20rpx 0 0;
		padding: 30rpx;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.section-more {
		font-size: 24rpx;
		color: #ff6b6b;
	}

	.products-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
	}

	.product-card {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		transition: transform 0.3s ease;
	}

	.product-card:active {
		transform: scale(0.98);
	}

	.product-image {
		width: 100%;
		height: 200rpx;
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.product-emoji {
		font-size: 60rpx;
	}

	.product-info {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.product-name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}

	.product-desc {
		font-size: 22rpx;
		color: #999;
	}

	.product-price-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 10rpx;
	}

	.product-price {
		font-size: 28rpx;
		font-weight: bold;
		color: #ff6b6b;
	}

	.add-btn {
		width: 50rpx;
		height: 50rpx;
		background-color: #ff6b6b;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: transform 0.2s ease;
	}

	.add-btn:active {
		transform: scale(0.9);
	}

	.add-icon {
		color: #fff;
		font-size: 24rpx;
		font-weight: bold;
	}

	/* 推荐区域样式 */
	.recommend-section {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin: 0 20rpx;
	}

	.recommend-scroll {
		white-space: nowrap;
	}

	.recommend-container {
		display: flex;
		gap: 20rpx;
		padding-bottom: 10rpx;
	}

	.recommend-card {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 15rpx;
		min-width: 160rpx;
		padding: 20rpx;
		border-radius: 15rpx;
		background-color: #fafafa;
		transition: transform 0.3s ease;
	}

	.recommend-card:active {
		transform: scale(0.95);
	}

	.recommend-image {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.recommend-emoji {
		font-size: 40rpx;
	}

	.recommend-name {
		font-size: 24rpx;
		color: #333;
		text-align: center;
		white-space: nowrap;
	}

	.recommend-price {
		font-size: 22rpx;
		color: #ff6b6b;
		font-weight: bold;
	}

	/* 全局样式优化 */
	.container {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	/* 滚动条隐藏 */
	::-webkit-scrollbar {
		display: none;
	}

	/* 响应式适配 */
	@media (max-width: 750rpx) {
		.products-grid {
			grid-template-columns: 1fr;
		}

		.banner-item {
			padding: 30rpx;
		}

		.banner-title {
			font-size: 32rpx;
		}
	}
</style>
