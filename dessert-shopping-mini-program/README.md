# 甜品购物小程序

一个基于 UniApp (Vue3) 开发的现代风格甜品购物小程序。

## 功能特色

### 🏠 首页
- **搜索栏**: 快速搜索甜品
- **轮播图**: 展示最新活动和优惠信息
- **分类标签栏**: 支持横向滚动的商品分类切换
- **商品展示**: 网格布局展示各类甜品
- **推荐区域**: 横向滚动的今日推荐商品

### 🛒 购物车
- **商品管理**: 增减数量、删除商品
- **全选功能**: 一键选择/取消所有商品
- **价格计算**: 实时计算选中商品总价
- **结算功能**: 支持批量结算选中商品
- **空购物车状态**: 友好的空状态提示

### 📱 商品详情
- **商品展示**: 大图展示商品信息
- **规格选择**: 支持多种规格选择
- **数量调节**: 灵活的数量增减控制
- **价格计算**: 实时计算总价
- **操作按钮**: 加入购物车和立即购买

### 👤 个人中心
- **用户信息**: 头像、昵称、会员等级、积分
- **会员特权**: 展示VIP专享权益
- **订单统计**: 各状态订单数量统计
- **功能菜单**: 收藏、优惠券、地址管理等
- **设置选项**: 系统设置和帮助反馈

## 设计特点

### 🎨 现代化UI设计
- **色彩搭配**: 以温暖的粉红色 (#ff6b6b) 为主色调
- **圆角设计**: 大量使用圆角元素，提升视觉舒适度
- **卡片布局**: 清晰的信息层级和视觉分组
- **渐变效果**: 适度使用渐变增强视觉效果

### 📱 交互体验
- **触摸反馈**: 所有可点击元素都有按压效果
- **平滑动画**: 使用CSS transition提升交互流畅度
- **响应式布局**: 适配不同屏幕尺寸
- **加载状态**: 友好的加载和空状态提示

### 🛠 技术实现
- **Vue3 Composition API**: 使用最新的Vue3语法
- **响应式数据**: 充分利用Vue3的响应式系统
- **组件化开发**: 良好的代码组织和复用性
- **现代CSS**: 使用Flexbox和Grid布局

## 项目结构

```
dessert-shopping-mini-program/
├── pages/
│   ├── index/           # 首页
│   │   └── index.vue
│   ├── product/         # 商品详情页
│   │   └── product.vue
│   ├── cart/           # 购物车页
│   │   └── cart.vue
│   └── profile/        # 个人中心页
│       └── profile.vue
├── static/             # 静态资源
├── App.vue            # 应用入口
├── main.js            # 主入口文件
├── pages.json         # 页面配置
└── manifest.json      # 应用配置
```

## 主要页面功能

### 首页 (pages/index/index.vue)
- 搜索功能
- 轮播广告
- 分类切换标签栏 (核心功能)
- 商品网格展示
- 推荐商品横向滚动

### 商品详情 (pages/product/product.vue)
- 商品图片展示
- 规格选择
- 数量调节
- 价格计算
- 购买操作

### 购物车 (pages/cart/cart.vue)
- 商品列表管理
- 选择/取消选择
- 数量修改
- 价格统计
- 批量操作

### 个人中心 (pages/profile/profile.vue)
- 用户信息展示
- 会员权益
- 订单统计
- 功能菜单
- 设置选项

## 技术要点

1. **使用 `view` 替代 `div`**: 严格按照UniApp规范
2. **Vue3 Composition API**: 现代化的代码组织方式
3. **响应式设计**: 适配各种设备屏幕
4. **组件化开发**: 良好的代码复用和维护性
5. **现代CSS**: Flexbox、Grid、动画效果

## 运行说明

1. 确保已安装 HBuilderX 或配置好 UniApp 开发环境
2. 导入项目到开发工具
3. 选择运行到微信小程序或其他平台
4. 在对应平台的开发者工具中预览效果

## 注意事项

- 项目使用Vue3语法，需要支持Vue3的UniApp版本
- 所有布局元素使用 `view` 而非 `div`
- 图片和图标路径需要根据实际资源进行调整
- 可根据实际需求添加更多功能和页面
