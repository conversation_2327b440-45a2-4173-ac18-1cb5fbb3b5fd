<template>
  <view class="modal-overlay" v-if="visible" @click="closeModal">
    <view class="modal-content" @click.stop>
      <!-- 标题 -->
      <view class="modal-header">
        <view class="app-icon">
          <text class="icon-text">🍰</text>
        </view>
        <text class="app-name">甜品小屋 申请</text>
      </view>

      <!-- 副标题 -->
      <view class="modal-subtitle">
        <text class="subtitle-text">获取并验证您的手机号</text>
      </view>

      <!-- 手机号显示 -->
      <view class="phone-section">
        <text class="phone-number">{{ maskedPhone }}</text>
        <text class="verified-icon" v-if="isVerified">✓</text>
      </view>

      <!-- 按钮组 -->
      <view class="button-group">
        <view class="cancel-btn" @click="closeModal">
          <text class="cancel-text">拒绝</text>
        </view>
        <view class="confirm-btn" @click="handleLogin">
          <text class="confirm-text">允许</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close', 'login-success']);

// 数据
const phoneNumber = ref('13312345678'); // 模拟手机号
const isVerified = ref(true); // 模拟已验证状态

// 计算属性 - 脱敏手机号
const maskedPhone = computed(() => {
  if (!phoneNumber.value) return '';
  const phone = phoneNumber.value;
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
});

// 方法
const closeModal = () => {
  emit('close');
};

const handleLogin = () => {
  // 模拟登录成功
  const userInfo = {
    name: '橙子',
    phone: phoneNumber.value,
    level: 'VIP会员',
    points: 1280,
    isLoggedIn: true
  };

  // 存储到本地
  uni.setStorageSync('userInfo', userInfo);

  // 通知父组件登录成功
  emit('login-success', userInfo);

  // 关闭弹窗
  closeModal();

  // 显示成功提示
  uni.showToast({
    title: '登录成功',
    icon: 'success'
  });
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 40rpx;
  position: relative;
  min-width: 600rpx;
  max-width: 80%;
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.app-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #4caf50;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: 40rpx;
  color: white;
}

.app-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-subtitle {
  margin-bottom: 40rpx;
}

.subtitle-text {
  font-size: 28rpx;
  color: #666;
}

.phone-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 60rpx;
}

.phone-number {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.verified-icon {
  font-size: 40rpx;
  color: #4caf50;
  font-weight: bold;
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cancel-btn {
  background-color: #f5f5f5;
}

.cancel-btn:active {
  background-color: #e0e0e0;
}

.confirm-btn {
  background-color: #4caf50;
}

.confirm-btn:active {
  background-color: #45a049;
}

.cancel-text {
  font-size: 32rpx;
  color: #666;
}

.confirm-text {
  font-size: 32rpx;
  color: white;
  font-weight: 500;
}
</style>
