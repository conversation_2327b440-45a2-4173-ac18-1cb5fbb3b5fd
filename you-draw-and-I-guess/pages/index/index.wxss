/* 容器样式 */
.container {
  height: 100vh;
  background-color: wheat;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 画布容器 - 用于相对定位 */
.canvas-container {
  position: relative;
  width: 700rpx;
  height: 700rpx;
  margin: 20rpx auto;
}

/* 画布样式 */
#myCanvas {
  width: 700rpx;
  height: 700rpx;
  background-color: lightyellow;
  border: 1rpx solid #ddd;
  display: block;
}

/* 临时画布 - 用于形状预览 */
.temp-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 700rpx;
  height: 700rpx;
  pointer-events: none;
  z-index: 10;
}

/* 图片直接绘制到主画布上，不需要独立的图片画布样式 */

/* 工具栏样式 */
.tool-box {
  width: 100%;
  margin-top: 20rpx;
}

.tool-bar {
  margin-bottom: 20rpx;
  justify-content: space-around;
}

.tool-options {
  width: 100%;
  margin-bottom: 20rpx;
}

.option-group {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

/* 选择器样式 */
.width-selector, .color-selector {
  margin: 0 10rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
}

.width-selector {
  background-color: black;
}

.color-selector {
  width: 40rpx;
  height: 40rpx;
}

/* 形状按钮样式 */
.shape-btn {
  padding: 10rpx 20rpx;
  margin: 0 10rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 24rpx;
}

/* 激活状态 */
.active {
  border-color: #07C160;
  box-shadow: 0 0 5rpx rgba(7, 193, 96, 0.5);
}

/* 保存按钮 */
.save-btn {
  margin-top: 30rpx;
  width: 80%;
  background-color: #07C160;
  color: white;
}

/* 确认按钮区域 - 固定定位浮动显示 */
.confirm-box {
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-around;
  width: 80%;
  max-width: 600rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  z-index: 1000;
  gap: 20rpx;
  animation: slideUp 0.3s ease-out;
}

/* 滑入动画 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(100rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.confirm-btn {
  background-color: #07C160;
  color: white;
  border-radius: 15rpx;
  flex: 1;
  padding: 20rpx;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

.confirm-btn:active {
  background-color: #06A050;
  transform: scale(0.95);
}

.cancel-btn {
  background-color: #FF4D4F;
  color: white;
  border-radius: 15rpx;
  flex: 1;
  padding: 20rpx;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

.cancel-btn:active {
  background-color: #E03E40;
  transform: scale(0.95);
}