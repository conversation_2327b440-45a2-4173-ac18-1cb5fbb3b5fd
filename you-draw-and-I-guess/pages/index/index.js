// 画布相关变量
let canvas = null
let ctx = null
let dpr = 1
let tempCanvas = null
let tempCtx = null
// 移除独立的图片画布，图片直接绘制到主画布上

// 绘图参数
let lineWidth = 10
let color = 'black'
let bgColor = 'lightyellow'
let lastX, lastY

Page({
  data: {
    // 工具状态
    currentTool: 'brush',
    isRubber: false,
    
    // 形状绘制
    currentShape: null,
    isDrawingShape: false,
    startX: 0,
    startY: 0,
    currentEndX: 0,
    currentEndY: 0,
    
    // 图片编辑
    selectedImage: null,
    isEditingImage: false,
    
    // 画笔选项
    widthSelector: [
      { lineWidth: 10, active: true },
      { lineWidth: 20, active: false },
      { lineWidth: 30, active: false },
      { lineWidth: 40, active: false },
      { lineWidth: 50, active: false }
    ],
    colorSelector: [
      { color: 'red', active: false },
      { color: 'orange', active: false },
      { color: 'yellow', active: false },
      { color: 'green', active: false },
      { color: 'blue', active: false },
      { color: 'purple', active: false },
      { color: 'black', active: true },
      { color: 'white', active: false }
    ],

    isEditingImage: false,
    cropStartX: 0,
    cropStartY: 0,
    cropEndX: 0,
    cropEndY: 0,
    scaleFactor: 1,
    rotateAngle: 0,
    
    // 确认按钮状态
    showConfirm: false


  },

  // 生命周期函数
  onReady() {
    this.initMainCanvas()
    this.initTempCanvas()
  },

  // 初始化主画布
  initMainCanvas() {
    wx.createSelectorQuery()
      .select('#myCanvas')
      .fields({ node: true, size: true })
      .exec(res => {
        if (!res || !res[0]) {
          console.error('主画布初始化失败')
          setTimeout(() => this.initMainCanvas(), 100)
          return
        }
        
        const { width, height, node } = res[0]
        canvas = node
        ctx = canvas.getContext('2d')
        dpr = wx.getSystemInfoSync().pixelRatio
        
        canvas.width = width * dpr
        canvas.height = height * dpr
        ctx.scale(dpr, dpr)
        
        ctx.lineWidth = lineWidth / dpr
        ctx.lineJoin = 'round'
        ctx.lineCap = 'round'
        ctx.strokeStyle = color
        
        this.clearCanvas()
      })
  },

  // 初始化临时画布
  initTempCanvas() {
    wx.createSelectorQuery()
      .select('#tempCanvas')
      .fields({ node: true, size: true })
      .exec(res => {
        if (!res || !res[0]) {
          console.error('临时画布初始化失败')
          // 重试初始化
          setTimeout(() => this.initTempCanvas(), 100)
          return
        }

        const { width, height, node } = res[0]
        tempCanvas = node
        tempCtx = tempCanvas.getContext('2d')

        // 确保临时画布与主画布尺寸一致
        tempCanvas.width = canvas.width
        tempCanvas.height = canvas.height
        tempCtx.scale(dpr, dpr)

        // 设置绘图样式
        tempCtx.lineJoin = 'round'
        tempCtx.lineCap = 'round'
        tempCtx.globalAlpha = 0.8 // 半透明预览

        console.log('临时画布初始化成功')
      })
  },



  // 工具选择
  selectTool(e) {
    const tool = e.currentTarget.dataset.tool
    this.setData({ 
      currentTool: tool,
      currentShape: null,
      isEditingImage: false
    })
  },

  // 画笔粗细选择
  chooseWidth(e) {
    lineWidth = e.currentTarget.dataset.w
    const widthSelector = this.data.widthSelector.map(item => ({
      ...item,
      active: item.lineWidth === lineWidth
    }))
    
    this.setData({ widthSelector })
    ctx.lineWidth = lineWidth / dpr
  },

  // 画笔颜色选择
  chooseColor(e) {
    color = e.currentTarget.dataset.color
    const colorSelector = this.data.colorSelector.map(item => ({
      ...item,
      active: item.color === color
    }))
    
    this.setData({ 
      colorSelector,
      isRubber: false 
    })
    ctx.strokeStyle = color
  },

  // 橡皮擦切换
  rubber() {
    const isRubber = !this.data.isRubber
    this.setData({ isRubber })
    ctx.strokeStyle = isRubber ? bgColor : color
  },

  // 清空画布
  clearCanvas() {
    ctx.fillStyle = bgColor
    ctx.fillRect(0, 0, canvas.width, canvas.height)
  },

  // 形状选择
  chooseShape(e) {
    const shape = e.currentTarget.dataset.shape
    this.setData({ 
      currentShape: this.data.currentShape === shape ? null : shape
    })
  },

  // 图片选择
  chooseImage() {
    // 优先使用新的 wx.chooseMedia API
    if (wx.chooseMedia) {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: res => {
          if (res.tempFiles && res.tempFiles.length > 0) {
            this.setData({ selectedImage: res.tempFiles[0].tempFilePath }, () => {
              this.drawSelectedImage()
            })
          }
        },
        fail: err => {
          console.error('选择图片失败:', err)
          wx.showToast({
            title: '选择图片失败',
            icon: 'error'
          })
        }
      })
    } else {
      // 兼容旧版本的 wx.chooseImage
      wx.chooseImage({
        count: 1,
        sourceType: ['album', 'camera'],
        success: res => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            this.setData({ selectedImage: res.tempFilePaths[0] }, () => {
              this.drawSelectedImage()
            })
          }
        },
        fail: err => {
          console.error('选择图片失败:', err)
          wx.showToast({
            title: '选择图片失败',
            icon: 'error'
          })
        }
      })
    }
  },

  // 绘制选中图片
  drawSelectedImage() {
    if (!canvas || !ctx) {
      console.error('主画布未初始化')
      return
    }

    if (!this.data.selectedImage) {
      console.error('未选择图片')
      return
    }

    const img = canvas.createImage()
    img.onload = () => {
      try {
        // 计算图片在画布中的合适尺寸（保持比例）
        const canvasWidth = canvas.width / dpr
        const canvasHeight = canvas.height / dpr
        const imgRatio = img.width / img.height
        const canvasRatio = canvasWidth / canvasHeight

        let drawWidth, drawHeight, drawX, drawY

        if (imgRatio > canvasRatio) {
          // 图片更宽，以宽度为准
          drawWidth = canvasWidth * 0.8  // 留一些边距
          drawHeight = drawWidth / imgRatio
        } else {
          // 图片更高，以高度为准
          drawHeight = canvasHeight * 0.8  // 留一些边距
          drawWidth = drawHeight * imgRatio
        }

        // 居中绘制
        drawX = (canvasWidth - drawWidth) / 2
        drawY = (canvasHeight - drawHeight) / 2

        ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight)
        console.log('图片绘制成功')

        wx.showToast({
          title: '图片加载成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('绘制图片失败:', error)
        wx.showToast({
          title: '图片加载失败',
          icon: 'error'
        })
      }
    }
    img.onerror = (error) => {
      console.error('图片加载失败:', error)
      wx.showToast({
        title: '图片加载失败',
        icon: 'error'
      })
    }
    img.src = this.data.selectedImage
  },

  // 统一触摸事件处理
  handleTouchStart(e) {
    if (this.data.currentTool === 'brush') {
      const { x, y } = e.touches[0]
      lastX = x
      lastY = y
      this.drawLine(x, y)
    } 
    else if (this.data.currentTool === 'shape' && this.data.currentShape) {
      const { x, y } = e.touches[0]
      this.setData({
        isDrawingShape: true,
        startX: x,
        startY: y
      })
    }
    else if (this.data.currentTool === 'image' && this.data.isEditingImage) {
      this.handleImageEditStart(e)
    }
  },

  handleTouchMove(e) {
    if (this.data.currentTool === 'brush') {
      const { x, y } = e.touches[0]
      this.drawLine(x, y)
      lastX = x
      lastY = y
    }
    else if (this.data.currentTool === 'shape' && this.data.isDrawingShape) {
      const { x, y } = e.touches[0]
      // 记录当前结束坐标
      this.setData({
        currentEndX: x,
        currentEndY: y
      })
      this.previewShape(x, y)
    }
    else if (this.data.currentTool === 'image' && this.data.isEditingImage) {
      this.handleImageEditMove(e)
    }
  },

  handleTouchEnd(e) {
    if (this.data.currentTool === 'shape' && this.data.isDrawingShape) {
      // 确保有结束坐标
      if (e.changedTouches && e.changedTouches[0]) {
        const { x, y } = e.changedTouches[0]
        this.setData({
          currentEndX: x,
          currentEndY: y
        })
      }
      this.commitShape()
      this.setData({
        isDrawingShape: false,
        currentEndX: 0,
        currentEndY: 0
      })
    }
  },

  // 基础绘图函数
  drawLine(x, y) {
    ctx.beginPath()
    ctx.moveTo(lastX, lastY)
    ctx.lineTo(x, y)
    ctx.stroke()
  },

  // 形状预览
  previewShape(endX, endY) {
    if (!tempCanvas || !tempCtx) {
      console.error('临时画布未初始化')
      return
    }

    this.clearTempCanvas()
    const { startX, startY, currentShape } = this.data

    // 设置临时画布样式
    tempCtx.strokeStyle = this.data.isRubber ? bgColor : color
    tempCtx.lineWidth = lineWidth / dpr
    tempCtx.lineJoin = 'round'
    tempCtx.lineCap = 'round'

    switch(currentShape) {
      case 'line':
        tempCtx.beginPath()
        tempCtx.moveTo(startX, startY)
        tempCtx.lineTo(endX, endY)
        tempCtx.stroke()
        break

      case 'rect':
        tempCtx.beginPath()
        tempCtx.rect(startX, startY, endX - startX, endY - startY)
        tempCtx.stroke()
        break

      case 'circle':
        const radius = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2))
        tempCtx.beginPath()
        tempCtx.arc(startX, startY, radius, 0, Math.PI * 2)
        tempCtx.stroke()
        break

      case 'triangle':
        tempCtx.beginPath()
        tempCtx.moveTo(startX, startY)
        tempCtx.lineTo(endX, endY)
        tempCtx.lineTo(startX * 2 - endX, endY)
        tempCtx.closePath()
        tempCtx.stroke()
        break
    }
  },

  // 清空临时画布
  clearTempCanvas() {
    if (tempCanvas && tempCtx) {
      tempCtx.clearRect(0, 0, tempCanvas.width / dpr, tempCanvas.height / dpr)
    }
  },

  // 确认形状绘制
  commitShape() {
    if (!tempCanvas || !tempCtx || !ctx) {
      console.error('画布未初始化')
      return
    }

    // 直接在主画布上绘制形状，而不是复制临时画布
    const { startX, startY, currentShape } = this.data
    const endX = this.data.currentEndX || startX
    const endY = this.data.currentEndY || startY

    // 设置主画布绘图样式
    ctx.strokeStyle = this.data.isRubber ? bgColor : color
    ctx.lineWidth = lineWidth / dpr
    ctx.lineJoin = 'round'
    ctx.lineCap = 'round'

    // 在主画布上绘制最终形状
    switch(currentShape) {
      case 'line':
        ctx.beginPath()
        ctx.moveTo(startX, startY)
        ctx.lineTo(endX, endY)
        ctx.stroke()
        break

      case 'rect':
        ctx.beginPath()
        ctx.rect(startX, startY, endX - startX, endY - startY)
        ctx.stroke()
        break

      case 'circle':
        const radius = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2))
        ctx.beginPath()
        ctx.arc(startX, startY, radius, 0, Math.PI * 2)
        ctx.stroke()
        break

      case 'triangle':
        ctx.beginPath()
        ctx.moveTo(startX, startY)
        ctx.lineTo(endX, endY)
        ctx.lineTo(startX * 2 - endX, endY)
        ctx.closePath()
        ctx.stroke()
        break
    }

    // 清空临时画布
    this.clearTempCanvas()
  },

  // 图片编辑功能
  startCrop() {
    this.setData({
      isEditingImage: 'crop',
      cropStartX: 0,
      cropStartY: 0,
      cropEndX: 0,
      cropEndY: 0,
      showConfirm: true
    })
  },

  startScale() {
    this.setData({
      isEditingImage: 'scale',
      scaleStartX: 0,
      scaleStartY: 0,
      scaleFactor: 1,
      showConfirm: true
    })
  },

  startRotate() {
    this.setData({
      isEditingImage: 'rotate',
      rotateAngle: 0,
      showConfirm: true
    })
  },

  // 保存画布到相册
  finishCanvas() {
    wx.canvasToTempFilePath({
      canvas,
      success: res => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({ title: '保存成功' })
          },
          fail: () => {
            wx.showToast({ title: '保存失败', icon: 'error' })
          }
        })
      },
      fail: err => {
        console.error('保存失败:', err)
        wx.showToast({ title: '保存失败', icon: 'error' })
      }
    })
  },



// 图片编辑开始
handleImageEditStart(e) {
  const { x, y } = e.touches[0]
  
  if (this.data.isEditingImage === 'crop') {
    this.setData({
      cropStartX: x,
      cropStartY: y,
      cropEndX: x,
      cropEndY: y
    })
  } 
  else if (this.data.isEditingImage === 'scale') {
    this.setData({
      scaleStartX: x,
      scaleStartY: y
    })
  }
  else if (this.data.isEditingImage === 'rotate') {
    this.setData({
      rotateStartX: x,
      rotateStartY: y
    })
  }
},

// 图片编辑过程
handleImageEditMove(e) {
  const { x, y } = e.touches[0]
  
  if (this.data.isEditingImage === 'crop') {
    this.setData({
      cropEndX: x,
      cropEndY: y
    })
    this.previewCrop()
  } 
  else if (this.data.isEditingImage === 'scale') {
    const distance = Math.sqrt(
      Math.pow(x - this.data.scaleStartX, 2) + 
      Math.pow(y - this.data.scaleStartY, 2)
    )
    const scaleFactor = 1 + distance / 100
    this.setData({ scaleFactor })
    this.previewScale()
  }
  else if (this.data.isEditingImage === 'rotate') {
    const angle = Math.atan2(
      y - this.data.rotateStartY, 
      x - this.data.rotateStartX
    ) * 180 / Math.PI
    this.setData({ rotateAngle: angle })
    this.previewRotate()
  }
},

// 预览裁剪效果
previewCrop() {
  const { cropStartX, cropStartY, cropEndX, cropEndY } = this.data
  this.clearTempCanvas()
  
  // 绘制裁剪区域
  tempCtx.strokeStyle = '#07C160'
  tempCtx.lineWidth = 2
  tempCtx.setLineDash([5, 5])
  tempCtx.strokeRect(
    cropStartX, cropStartY,
    cropEndX - cropStartX, cropEndY - cropStartY
  )
  tempCtx.setLineDash([])
},

// 预览缩放效果
previewScale() {
  if (!tempCanvas || !tempCtx) return
  this.clearTempCanvas()
  const img = tempCanvas.createImage()
  img.onload = () => {
    const centerX = canvas.width / (2 * dpr)
    const centerY = canvas.height / (2 * dpr)
    const width = img.width * this.data.scaleFactor
    const height = img.height * this.data.scaleFactor

    tempCtx.drawImage(
      img,
      centerX - width/2, centerY - height/2,
      width, height
    )
  }
  img.src = this.data.selectedImage
},

// 预览旋转效果
previewRotate() {
  if (!tempCanvas || !tempCtx) return
  this.clearTempCanvas()
  const img = tempCanvas.createImage()
  img.onload = () => {
    const centerX = canvas.width / (2 * dpr)
    const centerY = canvas.height / (2 * dpr)

    tempCtx.save()
    tempCtx.translate(centerX, centerY)
    tempCtx.rotate(this.data.rotateAngle * Math.PI / 180)
    tempCtx.drawImage(
      img,
      -img.width/2, -img.height/2,
      img.width, img.height
    )
    tempCtx.restore()
  }
  img.src = this.data.selectedImage
},

// 确认图片编辑
confirmImageEdit() {
  if (this.data.isEditingImage === 'crop') {
    this.applyCrop()
  }
  else if (this.data.isEditingImage === 'scale') {
    this.applyScale()
  }
  else if (this.data.isEditingImage === 'rotate') {
    this.applyRotate()
  }

  this.setData({
    isEditingImage: false,
    showConfirm: false
  })
  this.clearTempCanvas()
},

// 取消图片编辑
cancelImageEdit() {
  this.setData({
    isEditingImage: false,
    showConfirm: false
  })
  this.clearTempCanvas()
},

// 应用裁剪
applyCrop() {
  if (!canvas || !ctx) return
  const { cropStartX, cropStartY, cropEndX, cropEndY } = this.data
  const width = Math.abs(cropEndX - cropStartX)
  const height = Math.abs(cropEndY - cropStartY)

  const img = canvas.createImage()
  img.onload = () => {
    // 清空画布并重新绘制背景
    this.clearCanvas()

    // 绘制裁剪后的图片
    ctx.drawImage(
      img,
      Math.min(cropStartX, cropEndX), Math.min(cropStartY, cropEndY),
      width, height,
      0, 0, width, height
    )
  }
  img.src = this.data.selectedImage
},

// 应用缩放
applyScale() {
  if (!canvas || !ctx) return
  const img = canvas.createImage()
  img.onload = () => {
    const centerX = canvas.width / (2 * dpr)
    const centerY = canvas.height / (2 * dpr)
    const width = img.width * this.data.scaleFactor
    const height = img.height * this.data.scaleFactor

    // 清空画布并重新绘制背景
    this.clearCanvas()

    // 绘制缩放后的图片
    ctx.drawImage(
      img,
      centerX - width/2, centerY - height/2,
      width, height
    )
  }
  img.src = this.data.selectedImage
},

// 应用旋转
applyRotate() {
  if (!canvas || !ctx) return
  const img = canvas.createImage()
  img.onload = () => {
    const centerX = canvas.width / (2 * dpr)
    const centerY = canvas.height / (2 * dpr)

    // 清空画布并重新绘制背景
    this.clearCanvas()

    // 绘制旋转后的图片
    ctx.save()
    ctx.translate(centerX, centerY)
    ctx.rotate(this.data.rotateAngle * Math.PI / 180)
    ctx.drawImage(
      img,
      -img.width/2, -img.height/2,
      img.width, img.height
    )
    ctx.restore()
  }
  img.src = this.data.selectedImage
},




  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    
  }
})