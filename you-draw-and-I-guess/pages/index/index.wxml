<view class="container flexV">
  <!-- 画布容器 -->
  <view class="canvas-container">
    <!-- 主画布 -->
    <canvas id="myCanvas" type="2d"
            bindtouchstart="handleTouchStart"
            bindtouchmove="handleTouchMove"
            bindtouchend="handleTouchEnd"></canvas>

    <!-- 临时画布(用于形状预览) -->
    <canvas id="tempCanvas" type="2d" class="temp-canvas"></canvas>
  </view>

  <!-- 图片直接绘制到主画布上，不需要独立的图片画布 -->

  <!-- 工具栏 -->
  <view class="tool-box">
    <!-- 工具切换 -->
    <view class="tool-bar flexH">
      <button data-tool="brush" bindtap="selectTool">画笔</button>
      <button data-tool="shape" bindtap="selectTool">形状</button>
      <button data-tool="image" bindtap="selectTool">图片</button>
    </view>
    
    <!-- 画笔工具选项 -->
    <view class="tool-options" wx:if="{{currentTool === 'brush'}}">
      <!-- 粗细选择 -->
      <view class="option-group">
        <text>粗细:</text>
        <view wx:for="{{widthSelector}}" wx:key="lineWidth" 
              class="width-selector {{item.active?'active':''}}"
              data-w="{{item.lineWidth}}" bindtap="chooseWidth"
              style="width:{{item.lineWidth}}rpx;height:{{item.lineWidth}}rpx;"></view>
      </view>
      
      <!-- 颜色选择 -->
      <view class="option-group">
        <text>颜色:</text>
        <view wx:for="{{colorSelector}}" wx:key="color" 
              class="color-selector {{item.active?'active':''}}"
              data-color="{{item.color}}" bindtap="chooseColor"
              style="background-color:{{item.color}};"></view>
      </view>
      
      <!-- 功能按钮 -->
      <view class="option-group">
        <button size="mini" bindtap="rubber">橡皮擦</button>
        <button type="warn" size="mini" bindtap="clearCanvas">清空</button>
      </view>
    </view>
    
    <!-- 形状工具选项 -->
    <view class="tool-options" wx:if="{{currentTool === 'shape'}}">
      <view class="shape-options">
        <view class="shape-btn {{currentShape==='line'?'active':''}}" 
              data-shape="line" bindtap="chooseShape">直线</view>
        <view class="shape-btn {{currentShape==='rect'?'active':''}}" 
              data-shape="rect" bindtap="chooseShape">矩形</view>
        <view class="shape-btn {{currentShape==='circle'?'active':''}}" 
              data-shape="circle" bindtap="chooseShape">圆形</view>
        <view class="shape-btn {{currentShape==='triangle'?'active':''}}" 
              data-shape="triangle" bindtap="chooseShape">三角形</view>
      </view>
    </view>
    
    <!-- 图片工具选项 -->
    <view class="tool-options" wx:if="{{currentTool === 'image'}}">
      <button size="mini" bindtap="chooseImage">选择图片</button>
      <button size="mini" disabled="{{!selectedImage}}" bindtap="startCrop">裁剪</button>
      <button size="mini" disabled="{{!selectedImage}}" bindtap="startScale">缩放</button>
      <button size="mini" disabled="{{!selectedImage}}" bindtap="startRotate">旋转</button>
    </view>
  </view>
  
  <!-- 保存按钮 -->
  <button class="save-btn" bindtap="finishCanvas">保存到相册</button>
</view>

<!-- 确认按钮浮动层 -->
<view class="confirm-box" wx:if="{{showConfirm}}">
  <button class="confirm-btn" bindtap="confirmImageEdit">✓ 确认</button>
  <button class="cancel-btn" bindtap="cancelImageEdit">✕ 取消</button>
</view>

