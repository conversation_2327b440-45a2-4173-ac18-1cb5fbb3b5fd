/* pages/order-list/index.wxss */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.search-bar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #f8f8f8;
}

.order-list {
  flex: 1;
  overflow-y: auto;
}

.order-card {
  background-color: #ffffff;
  border-radius: 8px;
  margin: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #f2f2f2;
}

.order-id {
  font-size: 14px;
  color: #666;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status-pending { background-color: #fff7e6; color: #fa8c16; }
.status-processing { background-color: #e6f7ff; color: #1890ff; }
.status-completed { background-color: #f6ffed; color: #52c41a; }
.status-cancelled { background-color: #fafafa; color: #bfbfbf; }

.card-body {
  padding: 12px 0;
}

.info-row {
  display: flex;
  font-size: 14px;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 70px;
  color: #999;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
  color: #333;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #f2f2f2;
}

.action-btn {
  margin-left: 10px;
  padding: 4px 12px;
  font-size: 14px;
  border-radius: 15px;
  line-height: 1.5;
  background-color: #fff;
  border: 1px solid #ddd;
  color: #333;
}

.action-btn.primary {
  background-color: #1890ff;
  color: #fff;
  border-color: #1890ff;
}

.action-btn::after {
  border: none;
}

.loading-footer {
  text-align: center;
  padding: 15px 0;
  color: #999;
  font-size: 14px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 100px;
  color: #999;
}