// pages/order-list/index.js
const STATUS_CONFIG = {
  1: { text: '待上门', style: 'status-pending' },
  2: { text: '回收中', style: 'status-processing' },
  3: { text: '已完成', style: 'status-completed' },
  4: { text: '已取消', style: 'status-cancelled' },
};

Page({

  /**
   * 页面的初始数据
   */
  data: {
    searchInputValue: '',
    orderList: [],
    allOrderList: [], // 用于客户端搜索过滤
    page: 1,
    pageSize: 10,
    loading: false,
    finished: false,
    statusConfig: STATUS_CONFIG,
    headerHeight: 0 // 导航栏+搜索栏总高度
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setHeaderHeight();
    this.loadData(true);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadData(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.loading || this.data.finished) {
      return;
    }
    this.loadData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 模拟从服务器获取数据
  _getMockOrders(page = 1, pageSize = 10) {
    return new Promise(resolve => {
      setTimeout(() => {
        const list = [];
        if (page > 3) { // 模拟加载3页后没有更多数据
          resolve({ list: [], finished: true });
          return;
        }
        for (let i = 0; i < pageSize; i++) {
          const id = (page - 1) * pageSize + i + 1;
          const status = (id % 4) + 1;
          list.push({
            id: `WX20240521-00${id}`,
            status: status,
            items: `废旧家电、书籍 (共${id % 5 + 1}件)`,
            address: `XX市XX区XX街道XX小区 ${id}栋 ${id}0${id}室`,
            time: `2024-05-${20 + (id % 5)} 14:30-16:30`,
          });
        }
        resolve({ list, finished: false });
      }, 800);
    });
  },

  loadData(isRefresh = false) {
    if (isRefresh) {
      this.setData({ page: 1, finished: false });
    }

    this.setData({ loading: true });

    return this._getMockOrders(this.data.page, this.data.pageSize).then(res => {
      const { list: newList, finished } = res;
      const currentList = isRefresh ? [] : this.data.orderList;
      const updatedList = currentList.concat(newList);
      
      this.setData({
        orderList: updatedList,
        loading: false,
        finished: finished,
        page: this.data.page + 1
      });

      if (isRefresh) {
        this.setData({ allOrderList: updatedList });
      } else {
         this.setData({ allOrderList: this.data.allOrderList.concat(newList) });
      }
    });
  },

  // WeUI SearchBar Event Handlers
  search: function (value) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve([{text: '搜索结果', value: 1}, {text: '搜索结果2', value: 2}])
        }, 200)
    })
  },
  selectResult: function (e) {
      console.log('select result', e.detail)
  },
  inputChange: function (e) {
    this.setData({ searchInputValue: e.detail.value });
    if (e.detail.value === '') {
      this.setData({ orderList: this.data.allOrderList });
    }
  },
  clear: function () {
    this.setData({
      searchInputValue: '',
      orderList: this.data.allOrderList,
    });
  },
  searchAction: function() {
    const keyword = this.data.searchInputValue.trim();
    if (keyword === '') {
      this.setData({ orderList: this.data.allOrderList });
      return;
    }
    const filteredList = this.data.allOrderList.filter(order => 
      order.id.includes(keyword) || order.items.includes(keyword)
    );
    this.setData({ orderList: filteredList });
  },
  setHeaderHeight() {
    const systemInfo = wx.getSystemInfoSync();
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    const navBarHeight = menuButtonInfo.bottom + (menuButtonInfo.top - systemInfo.statusBarHeight);
    const searchBarHeight = 56; // 搜索栏固定高度
    this.setData({
      headerHeight: navBarHeight + searchBarHeight,
    });
  }
})