<navigation-bar title="订单列表" back="{{false}}" color="black" background="#FFF" back="{{true}}"></navigation-bar>
<view class="page-container">
  <view class="search-bar-container" style="top: {{headerHeight - 56}}px;">
    <mp-searchbar 
      bindselectresult="selectResult" 
      search="{{search}}"
      bindinput="inputChange"
      bindclear="clear"
      bindblur="searchAction"
      value="{{searchInputValue}}"
      placeholder="搜索订单号或物品"
    ></mp-searchbar>
  </view>

  <view class="order-list" style="padding-top: {{40}}px;">
    <block wx:if="{{orderList.length > 0}}">
      <view class="order-card" wx:for="{{orderList}}" wx:key="id">
        <view class="card-header">
          <text class="order-id">订单号: {{item.id}}</text>
          <view class="status-tag {{statusConfig[item.status].style}}">
            {{statusConfig[item.status].text}}
          </view>
        </view>
        <view class="card-body">
          <view class="info-row">
            <text class="info-label">回收物品:</text>
            <text class="info-content">{{item.items}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">回收地址:</text>
            <text class="info-content">{{item.address}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">预约时间:</text>
            <text class="info-content">{{item.time}}</text>
          </view>
        </view>
        <view class="card-footer">
          <button class="action-btn" wx:if="{{item.status === 1 || item.status === 2}}">取消订单</button>
          <button class="action-btn" wx:if="{{item.status === 3 || item.status === 4}}">删除订单</button>
          <button class="action-btn primary">查看详情</button>
        </view>
      </view>
    </block>
    <block wx:else>
        <view class="empty-state">
            <text>暂无相关订单</text>
        </view>
    </block>
  </view>

  <view class="loading-footer">
    <text wx:if="{{loading}}">正在加载中...</text>
    <text wx:if="{{finished && orderList.length > 0}}">--- 我是有底线的 ---</text>
  </view>
</view>