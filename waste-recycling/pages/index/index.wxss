/**index.wxss**/
page {
  background-color: #f4f4f4;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 24rpx 24rpx 24rpx;
  display: flex;
  flex-direction: column;
}

/* 搜索区 */
.search-section {
  padding: 20rpx 0;
}
.search-input {
  background-color: #fff;
  border-radius: 30rpx;
  padding: 10rpx 24rpx;
  font-size: 28rpx;
}

/* 轮播图 */
.banner-swiper {
  height: 300rpx;
  border-radius: 20rpx;
  overflow: hidden;
}
.swiper-item-view {
  width: 100%;
  height: 156px;
  background-color: #ddd;
}

/* 导航区 */
.nav-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin-top: 24rpx;
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.nav-grid {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}
.nav-icon-placeholder {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}
.nav-text {
  font-size: 24rpx;
  color: #333;
}

/* 推荐列表 */
.recommend-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin-top: 24rpx;
  padding: 20rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.recommend-item {
  display: flex;
  margin-bottom: 20rpx;
}
.recommend-img-placeholder {
  width: 180rpx;
  height: 140rpx;
  background-color: #eee;
  border-radius: 10rpx;
  margin-right: 20rpx;
}
.recommend-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.recommend-title {
  font-size: 28rpx;
  font-weight: bold;
}
.recommend-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 环保科普 */
.tips-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin-top: 24rpx;
  padding: 20rpx;
}
.tips-scroll {
  white-space: nowrap;
}
.tip-item {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
}
.tip-item:last-child {
  margin-right: 0;
}
.tip-img {
  width: 100%;
  height: 180rpx;
  border-radius: 10rpx;
  background-color: #eee;
}
.tip-title {
  font-size: 24rpx;
  margin-top: 10rpx;
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 回收流程 */
.process-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin-top: 24rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
}
.process-flow {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.process-icon {
  width: 60rpx;
  height: 60rpx !important;
}
.process-text {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.process-section .section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.go-to-order {
  font-size: 26rpx;
  font-weight: normal;
  color: #999;
}

.go-to-order::after {
  content: ' >';
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 用户信息 */
.user-section {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}
.login-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 40rpx;
}
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}
