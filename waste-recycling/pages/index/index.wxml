<!--index.wxml-->
<navigation-bar title="懒人回收" back="{{false}}" color="black" background="#FFF"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 搜索区 -->
    <view class="search-section">
      <input class="search-input" placeholder="请输入回收物品" confirm-type="search" bindconfirm="handleSearch" />
    </view>

    <!-- 轮播图  -->
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
      <block wx:for="{{swiperList}}" wx:key="id">
        <swiper-item>
          <view class="swiper-item-view">
            <image style="width: 100%;height: 156px;" class="swiper-item-img" src="{{item.url}}" />
          </view>
        </swiper-item>
      </block>
    </swiper>

    <!-- 内容导航 -->
    <view class="nav-section">
      <view class="nav-grid" wx:for="{{navList}}" wx:key="name">
        <image class="nav-icon-placeholder" src="{{item.url}}" />
        <text class="nav-text">{{item.name}}</text>
      </view>
    </view>

    <!-- 推荐列表 -->
    <view class="recommend-section">
      <view class="section-title">热门回收</view>
      <view class="recommend-list">
        <view class="recommend-item" wx:for="{{recommendList}}" wx:key="id">
          <image class="recommend-img-placeholder" src="{{item.url}}" />
          <view class="recommend-info">
            <view class="recommend-title">{{item.title}}</view>
            <view class="recommend-desc">{{item.desc}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 环保科普 -->
    <view class="tips-section">
      <view class="section-title">环保科普</view>
      <scroll-view class="tips-scroll" scroll-x>
        <view class="tip-item" wx:for="{{tipsList}}" wx:key="id">
          <image class="tip-img" src="{{item.img}}" mode="aspectFill"></image>
          <view class="tip-title">{{item.title}}</view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 回收流程 -->
    <view class="process-section">
      <view class="section-title">
        <text>回收流程</text>
        <text class="go-to-order" catchtap="navigateToOrder">去下单</text>
      </view>
      <view class="process-flow">
        <view class="process-step" wx:for="{{processList}}" wx:key="index">
          <image class="process-icon" src="{{item.icon}}" mode="widthFix"></image>
          <text class="process-text">{{item.text}}</text>
        </view>
      </view>
    </view>

  </view>
</scroll-view>
