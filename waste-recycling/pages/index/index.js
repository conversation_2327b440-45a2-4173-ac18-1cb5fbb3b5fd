// index.js
Page({
  data: {
    swiperList: [
      { id: 1, url: '/static/index-banner1.png' },
      { id: 2, url: '/static/index-banner2.webp' },
      { id: 3, url: '/static/index-banner3.webp' }
    ],
    navList: [
      { name: '金属回收', url: '/static/index/metal.png' },
      { name: '塑料回收', url: '/static/index/plastic.png' },
      { name: '电器回收', url: '/static/index/electric.png' },
      { name: '纸类回收', url: '/static/index/paper.png' },
      { name: '纺织品', url: '/static/index/textile.png' },
      { name: '玻璃', url: '/static/index/glass.png' },
      { name: '有害垃圾', url: '/static/index/hazardous.png' },
      { name: '其他', url: '/static/index/other.png' }
    ],
    recommendList: [
      { id: 1, title: '旧手机高价回收', desc: '环保处理，信息安全有保障', url: '/static/index/recovery-image1.jpeg' },
      { id: 2, title: '大家电上门回收', desc: '冰箱、洗衣机、空调等', url: '/static/index/recovery-image2.png' }
    ],
    tipsList: [
      { id: 1, title: '你知道吗？一个玻璃瓶回收后可以节省多少能源？', img: '/static/index/index-glass.jpeg'},
      { id: 2, title: '旧衣物改造的N种方法，让你的旧衣焕发新生！', img: '/static/index/index-laundry.webp'},
      { id: 3, title: '什么是"湿垃圾"？家庭垃圾分类小贴士', img: '/static/index/index-garbage.webp'}
    ],
    processList: [
      { icon: '/static/index/online-down.png', text: '线上下单' },
      { icon: '/static/arrow.png', text: '', type: 'arrow' },
      { icon: '/static/index/go-back.png', text: '上门回收' },
      { icon: '/static/arrow.png', text: '', type: 'arrow' },
      { icon: '/static/index/get-money.png', text: '获得收益' }
    ],
    userInfo: {},
    hasUserInfo: false,
  },

  onLoad() {
    // 可以在这里加载初始数据
  },

  onShareAppMessage() {

  },

  navigateToOrder() {
    wx.switchTab({
      url: '/pages/order/index',
    })
  },

  handleSearch(e) {
    const keyword = e.detail.value.trim();
    if (keyword) {
      wx.navigateTo({
        url: `/pages/search-list/index?keyword=${keyword}`,
      });
    }
  }
})
