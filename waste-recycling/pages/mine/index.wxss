/* pages/mine/index.wxss */
.page-container {
  background-color: #f4f4f5;
  min-height: 100vh;
}

.user-info-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #66bb6a, #43a047);
  padding: 50rpx 40rpx;
  border-bottom-left-radius: 50rpx;
  border-bottom-right-radius: 50rpx;
  height: 350rpx;
}

.user-info-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
}

.user-info-nickname {
  font-size: 38rpx;
  font-weight: bold;
  color: #ffffff;
}

.menu-list {
  padding: 0 30rpx;
  margin-top: -80rpx;
}

.menu-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden; /* 确保内部元素的圆角生效 */
}

.menu-list .weui-cells {
  margin-top: 0;
  background-color: transparent;
}

.menu-list .weui-cell {
  font-size: 30rpx;
  padding: 35rpx 30rpx;
}

.menu-list .weui-cell:before {
  left: 30rpx;
  right: 30rpx;
}

.menu-list .weui-cell__hd {
  margin-right: 30rpx;
  display: flex;
  align-items: center;
}