<navigation-bar title="个人中心" back="{{false}}" color="black" background="#FFF"></navigation-bar>
<view class="page-container">
  <!-- 用户信息 -->
  <view class="user-info-header" bindtap="updateUserProfile">
    <image class="user-info-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
    <text class="user-info-nickname">{{userInfo.nickName}}</text>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-list">
    <view class="menu-card">
      <mp-cells>
        <mp-cell wx:for="{{menuItems}}" wx:key="title" has-footer="{{true}}" url="{{item.url}}" bind:tap="handleToList" data-item="{{item}}">
          <view slot="icon">
            <mp-icon icon="{{item.icon}}" color="black" size="{{20}}"></mp-icon>
          </view>
          <view>{{item.title}}</view>
        </mp-cell>
      </mp-cells>
    </view>
  </view>
</view>