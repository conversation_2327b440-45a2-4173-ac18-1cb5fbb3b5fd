// pages/mine/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
      nickName: '点击获取头像昵称'
    },
    menuItems: [{
        icon: 'shop',
        title: '回收订单',
        url: '/pages/order-list/index'
      },
      {
        icon: 'location',
        title: '地址管理',
        url: ''
      },
      {
        icon: 'contacts',
        title: '联系客服',
        url: '' // 或者使用客服按钮
      },
      {
        icon: 'previous2',
        title: '意见反馈',
        url: ''
      },
      {
        icon: 'info',
        title: '关于我们',
        url: ''
      }
    ]
  },

  handleToList(e) {
    const url = e.currentTarget.dataset.item.url
    if (!url) {
      wx.showToast({
        title: '开发中',
        icon: 'none'
      })
      return
    }
    // const item = e.
    wx.navigateTo({
      url: '/pages/order-list/index',
    })
  },

  updateUserProfile() {
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        this.setData({
          'userInfo.avatarUrl': res.userInfo.avatarUrl,
          'userInfo.nickName': res.userInfo.nickName
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})