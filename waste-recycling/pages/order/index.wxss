/* pages/order/index.wxss */
page {
  background-color: #f7f8fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.container {
  padding: 24rpx;
}

/* 卡片统一样式 */
.card {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

/* 地址卡片 */
.address-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.address-content {
  flex: 1;
}

.address-content .placeholder {
  display: flex;
  align-items: center;
  color: #888;
}

.location-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
}

.user-info {
  margin-bottom: 8rpx;
}

.user-info .name {
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 20rpx;
}

.user-info .tel {
  font-size: 28rpx;
  color: #555;
}

.detail-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 废品分类 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  border-radius: 16rpx;
  border: 1px solid #eee;
  transition: all 0.2s ease-in-out;
}

.category-item.selected {
  border-color: #07c160;
  background-color: #e6f8ef;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
}

/* 图片上传 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-wrapper {
  position: relative;
  width: 150rpx;
  height: 150rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.remove-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  line-height: 40rpx;
}

.add-image-btn {
  width: 150rpx;
  height: 150rpx;
  background-color: #fafafa;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 60rpx;
  color: #999;
}

/* 备注 */
.notes-input {
  width: 100%;
  height: 160rpx;
  font-size: 28rpx;
  line-height: 1.5;
  padding: 16rpx;
  box-sizing: border-box;
  background-color: #f7f8fa;
  border-radius: 8rpx;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  background-image: linear-gradient(to right, #07c160, #06ad56);
  color: white;
  font-size: 32rpx;
  border-radius: 48rpx;
  margin-top: 20rpx;
  border: none;
}