<navigation-bar title="在线下单" back="{{false}}" color="black" background="#FFF"></navigation-bar>
<view class="container">
  <!-- 地址选择卡片 -->
  <view class="card address-card" bindtap="handleChooseAddress">
    <view class="address-content">
      <block wx:if="{{address}}">
        <view class="user-info">
          <text class="name">张三</text>
          <text class="tel">133****3901</text>
        </view>
        <view class="detail-address">{{address}}</view>
      </block>
      <block wx:else>
        <view class="placeholder">
          <image class="location-icon" src="/static/order/select.png"></image>
          <text>请选择上门地址</text>
        </view>
      </block>
    </view>
    <image class="arrow-icon" src="/static/order/arrow-right.png"></image>
  </view>

  <!-- 废品详情卡片 -->
  <view class="card category-card">
    <view class="card-title">请选择废品类型</view>
    <view class="category-grid">
      <view wx:for="{{categories}}" wx:key="name" data-index="{{index}}" class="category-item {{item.selected ? 'selected' : ''}}" bindtap="handleSelectCategory">
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 上传图片卡片 -->
  <view class="card image-card">
    <view class="card-title">上传图片 (可选)</view>
    <view class="image-grid">
      <view wx:for="{{images}}" wx:key="*this" class="image-wrapper">
        <image class="preview-image" src="{{item}}" mode="aspectFill"></image>
        <view class="remove-btn" data-index="{{index}}" bindtap="handleRemoveImage">×</view>
      </view>
      <view wx:if="{{images.length < 9}}" class="add-image-btn" bindtap="handleChooseImage">
        <text>+</text>
      </view>
    </view>
  </view>

  <!-- 备注信息卡片 -->
  <view class="card notes-card">
    <view class="card-title">备注信息</view>
    <textarea class="notes-input" maxlength="-1" placeholder="请输入特殊要求，如：东西较多，请携带推车" bindinput="handleNotesInput" value="{{notes}}"></textarea>
  </view>

  <!-- 提交按钮 -->
  <button class="submit-btn" bindtap="handleSubmit">立即下单</button>
</view>