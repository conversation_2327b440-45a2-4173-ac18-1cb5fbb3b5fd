// pages/order/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    address: '',
    categories: [{
        name: '纸类',
        selected: false
      },
      {
        name: '塑料',
        selected: false
      },
      {
        name: '金属',
        selected: false
      },
      {
        name: '家电',
        selected: false
      },
      {
        name: '纺织物',
        selected: false
      },
      {
        name: '玻璃',
        selected: false
      },
      {
        name: '电池',
        selected: false
      },
      {
        name: '其他',
        selected: false
      },
    ],
    images: [],
    notes: '',
    hasSelectedCategory: false, // 是否已选择分类
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 处理地址选择
   */
  handleChooseAddress() {
    wx.chooseLocation({
      success: (res) => {
        console.log('res', res)
        this.setData({
          // 可以选择只显示 name 或者更详细的 address
          address: res.name || res.address
        });
      },
      fail: (err) => {
        console.error('选择地区失败', err);
        if (err.errMsg.includes('auth deny')) {
          wx.showToast({
            title: '请授权位置信息',
            icon: 'none'
          });
        } else if (err.errMsg !== "chooseLocation:fail cancel") {
          wx.showToast({
            title: '选择失败',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 处理废品分类选择
   * @param {object} e 事件对象
   */
  handleSelectCategory(e) {
    const index = e.currentTarget.dataset.index;
    const categories = this.data.categories;
    categories[index].selected = !categories[index].selected;

    const hasSelectedCategory = categories.some(item => item.selected);

    this.setData({
      categories,
      hasSelectedCategory
    });
  },

  /**
   * 处理图片选择
   */
  handleChooseImage() {
    const count = 9 - this.data.images.length;
    wx.chooseMedia({
      count,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFiles.map(file => file.tempFilePath);
        this.setData({
          images: this.data.images.concat(tempFilePaths)
        });
      },
    });
  },

  /**
   * 处理图片移除
   * @param {object} e 事件对象
   */
  handleRemoveImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.images];
    images.splice(index, 1);
    this.setData({
      images
    });
  },

  /**
   * 处理备注信息输入
   * @param {object} e 事件对象
   */
  handleNotesInput(e) {
    this.setData({
      notes: e.detail.value
    });
  },

  /**
   * 处理提交订单
   */
  handleSubmit() {
    if (!this.data.address) {
      wx.showToast({
        title: '请选择地址',
        icon: 'none'
      });
      return;
    }

    if (!this.data.hasSelectedCategory) {
      wx.showToast({
        title: '请选择废品类型',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '正在下单...',
    });

    const selectedCategories = this.data.categories.filter(item => item.selected).map(item => item.name);

    // 模拟网络请求
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '下单成功',
        icon: 'success',
        duration: 2000
      });
      wx.navigateTo({
        url: '/pages/order-list/index',
      })
    }, 1500);
  },
})