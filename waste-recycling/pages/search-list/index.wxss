/* pages/search-list/index.wxss */
.container {
  padding: 0 32rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

.search-icon {
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
}

.result-list {
  margin-top: 20rpx;
}

.result-item {
  display: flex;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease-in-out;
}
.result-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}


.result-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
}

.result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.result-desc {
  font-size: 24rpx;
  color: #888;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #888888;
}