// pages/search-list/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    keyword: '',
    searchResult: [],
    showEmpty: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { keyword } = options;
    this.setData({
      keyword: keyword || ''
    });
    this.performSearch();
  },

  performSearch() {
    // 模拟搜索结果
    const dummyData = [
      {
        id: 1,
        image: '/static/index-banner1.png',
        title: '可回收垃圾',
        description: '这里是关于可回收垃圾的详细描述信息，帮助用户更好地进行垃圾分类。'
      },
      {
        id: 2,
        image: '/static/index-banner2.webp',
        title: '有害垃圾',
        description: '这里是关于有害垃圾的详细描述信息，包含常见类型和处理方法。'
      },
      {
        id: 3,
        image: '/static/index-banner3.webp',
        title: '厨余垃圾',
        description: '关于厨余垃圾的介绍，例如剩菜剩饭、果皮等，指导正确投放。'
      },
      {
        id: 4,
        image: '/static/index/index-other.png',
        title: '其他垃圾',
        description: '除上述三类之外的其他垃圾，如尘土、烟头等。'
      }
    ];

    // 模拟无结果的情况
    const result = this.data.keyword ? dummyData.filter(item => item.title.includes(this.data.keyword)) : dummyData;

    this.setData({
      searchResult: result,
      showEmpty: result.length === 0,
    });
  },

  onSearchInput(e) {
    this.setData({
      keyword: e.detail.value
    });
  },

  onSearchConfirm() {
    this.performSearch();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})